const express = require('express');
const app = express();
const PORT = 4000; // 您可以选择其他端口号
const host = 'https://devreport.cosmosaix.com/'
// app.get('/ureport/userdetialreport', (req, res) => {
//     res.redirect( host +'ureport/preview?_u=file:userDetailReport.ureport.xml');
// });
// app.get('/ureport/userTeamSumReport.ureport.xml', (req, res) => {
//     res.redirect( host +'ureport/preview?_u=file:userTeamSumReport.ureport.xml');
// });
// app.get('/ureport/userRewardReport.ureport.xml', (req, res) => {
//     res.redirect( host +'ureport/preview?_u=file:userRewardReport.ureport.xml');
// });
app.get('/ureport/:report', (req, res) => {
    let report =  req.params.report
    res.redirect( host +`ureport/preview?_u=file:${report}`);
});
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
