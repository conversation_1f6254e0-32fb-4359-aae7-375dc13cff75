{"name": "DBTouch", "version": "4.8.0", "description": "DBTouch", "author": "LionLi", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:stag": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/dromara/RuoYi-Vue-Plus.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "@smallwei/avue": "^2.9.11", "avue-plugin-ueditor": "^0.2.17", "axios": "^0.24.0", "cache-loader": "^2.0.1", "clipboard": "2.0.8", "codemirror": "^6.0.1", "core-js": "3.25.3", "echarts": "5.4.0", "element-ui": "^2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-codemirror-lite": "^1.0.4", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-jsonp": "^2.0.0", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}