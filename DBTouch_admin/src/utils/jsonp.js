// jsonp.js
export default function jsonp(url, callbackName) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = `${url}&callback=${callbackName}`;
      document.body.appendChild(script);
  
      window[callbackName] = function(data) {
        resolve(data);
        document.body.removeChild(script);
      };
  
      script.onerror = function() {
        reject(new Error("JSONP request failed"));
      };
    });
  }
  