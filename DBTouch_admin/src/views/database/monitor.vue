<template>
  <div class="app-container">
    <el-card
      v-for="(card, index) in cards"
      :key="index"
      class="hearCard"
      shadow="always"
      :header="card.header"
      :style="{ color: card.textColor,aspectRatio: '0.95' }"
      style="display: inline-block;"
    >
      {{ card.content }}
    </el-card>

    <div >
      <div ref="pieChart" style="display: inline-block; margin-top: 15px;border: 1px solid #ddd; /* 定义浅边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 定义浅阴影效果 */margin-right: 10px; width: 45%; height: 400px;"/>

      <div ref="pieChart2" style="display: inline-block;margin-top: 15px;border: 1px solid #ddd; /* 定义浅边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 定义浅阴影效果 */margin-left: 10px; width: 45%; height: 400px;"/>
    </div>

  </div>


</template>

<script>
import * as echarts from "echarts";
export default {
  name: 'Index',
  data() {
    return {
      cards: [
        {
          header:"未关闭告警数",
          content:"48",
        },
        {
          header:"监控总数",
          content:"14",
        },
        {
          header:"在线监控数",
          content:"11",
          textColor:"rgb(108,210,171)",
        },
        {
          header:"离线监控数",
          content:"3",
          textColor:"rgb(234, 173, 4)",
        },
        {
          header:"禁用监控数",
          content:"0",
          textColor:"rgb(223, 128, 117)"
        },
        {
          header:"MySQL总数",
          content:"10",
        },
        {
          header:"Oracle总数",
          content:"3",
        },
        {
          header:"PostgreSQL总数",
          content:"1",
        },
      ], // 存储从 JSON 数据中获取的卡片信息
    };
  },
  async created() {

  },

  mounted() {
    // 在 mounted 钩子中初始化饼状图
    this.initPieChart();
    this.initPieChart2();
  },

  methods: {
    initPieChart2(){
      const myChart = echarts.init(this.$refs.pieChart2);
      let option = {
        title: {
          text: ' 内存使用情况',
          textStyle: {
            color: '#333', // 标题文字颜色
            fontSize: 18, // 标题文字大小
            fontWeight: 'bold', // 标题文字粗细
          },
          left: 'left', //
          top: '10px', // 标题距离顶部的距离
        },
        series: [
          {
            type: 'gauge',
            axisLine: {
              lineStyle: {
                width: 30,
                color: [
                  [0.3, '#67e0e3'],
                  [0.7, '#37a2da'],
                  [1, '#fd666d']
                ]
              }
            },
            pointer: {
              itemStyle: {
                color: 'auto'
              }
            },
            axisTick: {
              distance: -30,
              length: 8,
              lineStyle: {
                color: '#fff',
                width: 2
              }
            },
            splitLine: {
              distance: -30,
              length: 30,
              lineStyle: {
                color: '#fff',
                width: 4
              }
            },
            axisLabel: {
              color: 'inherit',
              distance: 40,
              fontSize: 20
            },
            detail: {
              valueAnimation: true,
              formatter: '{value} ',
              color: 'inherit'
            },
            data: [
              {
                value: 70
              }
            ]
          }
        ]
      }
      myChart.setOption(option);
    },
    initPieChart() {
      const myChart = echarts.init(this.$refs.pieChart);
      const option = {
        title: {
          text: ' 警告统计',
          textStyle: {
            color: '#333', // 标题文字颜色
            fontSize: 18, // 标题文字大小
            fontWeight: 'bold', // 标题文字粗细
          },
          left: 'left', //
          top: '10px', // 标题距离顶部的距离
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#af0d0d',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 108, name: '慢查询' },
              { value: 75, name: '主要' },
              { value: 58, name: '次要' },
              { value: 44, name: '警告' },
              { value: 30, name: '错误' }
            ]
          }
        ]
      };
      myChart.setOption(option);
    }
  }
};
</script>

<style scoped>
.hearCard {
  display: inline-block;
  margin: 10px;
  width: 10%;
  text-align: center;
}
</style>
