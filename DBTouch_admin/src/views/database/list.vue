<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">

<!--      <el-button v-hasPermi="['dbManager:source:testConnect']" type="primary" size="small"-->
<!--                 @click="testConnect()">测试连接</el-button>-->
    </template>
      <template #menu="{size,row}">
        <el-button type="text" size="small" @click="testConnect(row)">测试连接</el-button>
      </template>
      <template #inUse="scope">
        <el-switch v-model="scope.row.inUse" :active-value="1" :inactive-value="0" @change="switChange(scope.row)"></el-switch>
      </template>
    </avue-crud>



  </div>
</template>
<!--<script src="@/js/thrift.js"/>-->
<!--<script src="@/js/gen-js/DbManager_types.js"/>-->
<!--<script src="@/js/gen-js/DbManagerService.js"/>-->

<script>

// import thrift from '@/js/thrift';
// import DbManagerService from '@/js/gen-js/DbManagerService';

import { makeInUse, addSource, list, testDataConnect } from '@/api/source'
import { getToken } from '@/utils/auth'
export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue:''
    }
  },
  computed: {
    option() {
      return {
        dialogDrag:true,
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: true,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'name',
            searchSpan: 4,
            search: true,
            editDisplay: true,
            addDisplay: true,
          },
          // {
          //   label: '当前数据源',
          //   prop: 'inUse',
          //   editDisplay: false,
          //   addDisplay: false,
          //   slot: 'avue-switch'
          // },
          {
            label: '数据库名',
            prop: 'databaseName',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '数据库类型',
            prop: 'type',
            type: 'select',
            search: true,
            searchSpan: 4,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '达梦',
                value: 1
              },
              {
                label: '人大金仓',
                value: 2
              },
              {
                label: '其他',
                value: 3
              }
            ],
          },
          {
            label: '端口号',
            prop: 'port',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '连接地址',
            prop: 'url',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '用户名',
            prop: 'username',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '密码',
            prop: 'password',
            type: 'password', // 设置输入框类型为密码
            search: false,
            formatter: () => '*****', // 将姓名替换为 **
            editDisplay: true,
            addDisplay: true,
          },

          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    testConnect(row){
      testDataConnect({id:row.id}).then(resp => {
        if (resp.code === 200) {
          this.$message.success('连接成功！')
        } else {
          this.$message.error('连接失败')
        }
      })
    },
    switChange(row){
      makeInUse({id:row.id}).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    rowSave(form, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id){
      this.$router.push('order?capitalId='+id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

