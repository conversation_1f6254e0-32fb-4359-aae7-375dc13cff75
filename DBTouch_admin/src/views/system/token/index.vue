<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>Token</span>
      </div>
      <div class="token-content">
        <el-input
          v-model="tokenValue"
          :readonly="true"
          type="text"
          class="token-input"
        />
        <el-button
          type="primary"
          icon="el-icon-copy-document"
          @click="copyToken"
        >
          复制
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="refreshToken"
        >
          刷新
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>

import { showToken, refreshToken } from '@/api/userToken'

export default {
  name: 'Token',
  data() {
    return {
      tokenValue: '' // 这里替换为实际的 token 值
    }
  },
  created() {
    this.getToken()
  },
  methods: {
    // 获取token
    
    async getToken() {
        showToken().then(resp => {
          if (resp.code === 200) {
            this.tokenValue = resp.data.token
          } else {
            this.$message.error('获取失败！')
          }
        })
      
    },
    copyToken() {
      const input = document.createElement('input')
      input.value = this.tokenValue
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message({
        message: 'Token已复制到剪贴板',
        type: 'success'
      })
    },
    async refreshToken() {
      const res = await refreshToken()
      if (res.code === 200) {
        this.tokenValue = res.data.token
        this.$message({
          message: 'Token已刷新',
          type: 'success'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.token-content {
  display: flex;
  align-items: center;
  gap: 16px;

  .token-input {
    flex: 1;
  }
}
</style> 