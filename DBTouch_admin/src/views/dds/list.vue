<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['dds:config:addBtn']" type="primary" icon="el-icon-plus" size="small"
                   @click="showModal(1)"
        >新 增
        </el-button>
      </template>
      <template #inUse="scope">
        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="switChange(scope.row)"
        ></el-switch>
      </template>
      <template #menu="{row,index}">
        <el-button v-hasPermi="['dds:config:editBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-edit"
                   @click="showModal(2,row)">编辑</el-button>
        <el-button v-hasPermi="['dds:config:lookBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-view"
                   @click="showModal(3,row)">查看</el-button>
        <el-button v-hasPermi="['dds:config:delBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-delete"
                   @click="rowDel(row,index)">删除</el-button>
      </template>
    </avue-crud>

    <el-dialog
      :visible.sync="centerDialogVisible"
      :title="modelTitle"
      width="80%"
      align-center
    >
      <el-row>
        <!-- 左侧部分 -->
        <el-col :span="showRightPanel ? 12 : 24">
          <div class="grid-content">
            <div class="form-item">
              <span class="form-label">配置名称：</span>
              <el-input  v-model="ddsConfig.name"  :disabled="readOnlyStatus" placeholder="请输入配置名称"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">备注：</span>
              <el-input type="textarea" v-model="ddsConfig.remake" :disabled="readOnlyStatus" :rows="2" placeholder="请输入备注"></el-input>
            </div>
            <div class="form-item" v-show="!readOnlyStatus">
              <span class="form-label">DDS配置：</span>
              <div class="input-box border dds-table-container" >
                <avue-crud
                  :option="infoOption"
                  :table-loading="loading"
                  :data="infoData"
                  :page.sync="infoPage"
                  @row-del="rowInfoDel"
                  @row-save="rowInfoSave"
                  @row-update="rowInfoUpdate"
                  @search-change="searchInfoChange"
                  @search-reset="searchInfoReset"
                  @size-change="sizeInfoChange"
                  @refresh-change="refreshInfoChange"
                  @current-change="currentInfoChange"
                >
                  <template #status="scope">
                    <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="changeInfoStatus(scope.row)"></el-switch>
                  </template>
                  <template slot="menuLeft" slot-scope="scope">
                    <el-button type="primary" size="small" @click="openGenerateModal">
                      生成主题
                    </el-button>
                    <el-button type="primary" size="small" @click="openDdsInfoDialog('add')">
                      新 增
                    </el-button>

                  </template>

                  <template #menu="{row,index}">
                    <el-button v-hasPermi="['dds:config:delBtn']"
                               type="text"
                               size="mini"
                               icon="el-icon-delete"
                               @click="rowInfoDel(row,index)">删除</el-button>
                    <el-button v-hasPermi="['dds:config:editBtn']"
                               type="text"
                               size="mini"
                               icon="el-icon-edit"
                               @click="openDdsInfoDialog('edit', row)">编辑</el-button>
                    <el-button v-hasPermi="['dds:config:lookBtn']"
                               type="text"
                               size="mini"
                               icon="el-icon-view"
                               @click="showAuthModal(row)">分配权限</el-button>
                  </template>
                </avue-crud>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 控制按钮 -->
        <div class="toggle-panel-btn">
          <el-button
            type="primary"
            size="small"
            @click="toggleRightPanel"
          >
            {{ showRightPanel ? '隐藏预览' : '显示预览' }}
          </el-button>
        </div>

        <!-- 右侧部分 -->
        <el-col :span="12" v-show="showRightPanel">
          <div class="grid-content">
            <span class="form-label">配置文件预览：</span>
            <el-input
              type="textarea"
              v-model="ddsConfig.shardingTextConfig"
              :disabled="true"
              :rows="23"
              placeholder="配置文件内容">
            </el-input>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <div v-if="readOnlyStatus">
            <el-button @click="centerDialogVisible = false">关 闭</el-button>
          </div>
          <div v-else>
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveConfig">
              确 认
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="用户权限分配"
      :visible.sync="userDialogVisible"
      width="60%"
    >
      <avue-crud
        :option="userOption"
        :table-loading="userLoading"
        :data="userData"
        :page.sync="userPage"
        @search-change="searchUserChange"
        @refresh-change="refreshUserChange"
      >
        <template #status="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="changeUserStatus(scope.row)"
          ></el-switch>
        </template>
      </avue-crud>
    </el-dialog>

    <!-- 添加新的弹窗 -->
    <el-dialog
      title="生成主题"
      :visible.sync="generateDialogVisible"
      width="60%"
    >
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="发布类型">
          <el-select
            v-model="generateForm.publishType"
            placeholder="请选择发布类型"
            @change="handlePublishTypeChange"
          >
            <el-option label="发布" :value="1"></el-option>
            <el-option label="执行" :value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="名称">
          <el-input v-model="generateForm.name" placeholder="请输入名称"></el-input>
        </el-form-item>

        <el-form-item label="频次">
          <el-input-number v-model="generateForm.frequency" :min="1" placeholder="请输入频次"></el-input-number>
        </el-form-item>

        <el-form-item label="数据源">
          <el-select v-model="generateForm.sourceId" placeholder="请选择数据源" @change="handleSourceChange">
            <el-option
              v-for="item in sourceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据库">
          <el-select v-model="generateForm.database" placeholder="请选择数据库" @change="handleDatabaseChange">
            <el-option
              v-for="item in databaseOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据表">
          <el-select v-model="generateForm.table" placeholder="请选择数据表" @change="handleTableChange">
            <el-option
              v-for="item in tableOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="SQL类型">
          <el-select
            v-model="generateForm.sqlType"
            placeholder="请选择SQL类型"
            @change="handleSqlTypeChange"
            :disabled="generateForm.publishType === 1"
          >
            <el-option
              v-for="option in sqlTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="generateForm.publishType === 1 && option.value !== '1'"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择字段" v-if="tableFields.length > 0">
          <el-checkbox v-if="generateForm.sqlType === 1" v-model="generateForm.useAllFields" @change="handleAllFieldsChange">查询所有字段 (*)</el-checkbox>
          <el-checkbox-group v-model="generateForm.selectedFields" @change="handleFieldsChange" :disabled="generateForm.useAllFields">
            <el-checkbox
              v-for="field in tableFields"
              :key="field.name"
              :label="field.name"
            >
              {{field.name}} ({{field.type}})
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 添加WHERE条件选项 -->
        <el-form-item label="查询条件" v-if="generateForm.sqlType === 1">
          <div v-for="(condition, index) in generateForm.whereConditions" :key="index" class="where-condition">
            <el-select v-model="condition.field" placeholder="选择字段" required>
              <el-option
                v-for="field in tableFields"
                :key="field.name"
                :label="field.name"
                :value="field.name"
              ></el-option>
            </el-select>
            <el-select v-model="condition.operator" placeholder="选择操作符">
              <el-option label="等于" value="="></el-option>
              <el-option label="大于" value=">"></el-option>
              <el-option label="小于" value="<"></el-option>
              <el-option label="大于等于" value=">="></el-option>
              <el-option label="小于等于" value="<="></el-option>
              <el-option label="不等于" value="!="></el-option>
              <el-option label="包含" value="LIKE"></el-option>
            </el-select>
            <el-input v-model="condition.value" placeholder="输入值" required></el-input>
            <el-button type="danger" icon="el-icon-delete" circle @click="removeWhereCondition(index)"></el-button>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="addWhereCondition">添加条件</el-button>
        </el-form-item>

        <!-- 添加分组、排序、分页选项 -->
        <el-form-item label="分组" v-if="generateForm.sqlType === 1">
          <el-select v-model="generateForm.groupByFields" multiple placeholder="选择分组字段">
            <el-option
              v-for="field in tableFields"
              :key="field.name"
              :label="field.name"
              :value="field.name"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="排序" v-if="generateForm.sqlType === 1">
          <div v-for="(order, index) in generateForm.orderByFields" :key="index" class="order-condition">
            <el-select v-model="order.field" placeholder="选择字段">
              <el-option
                v-for="field in tableFields"
                :key="field.name"
                :label="field.name"
                :value="field.name"
              ></el-option>
            </el-select>
            <el-select v-model="order.direction" placeholder="排序方式">
              <el-option label="升序" value="ASC"></el-option>
              <el-option label="降序" value="DESC"></el-option>
            </el-select>
            <el-button type="danger" icon="el-icon-delete" circle @click="removeOrderField(index)"></el-button>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="addOrderField">添加排序</el-button>
        </el-form-item>

        <!-- <el-form-item label="分页" v-if="generateForm.sqlType === 1">
          <el-input-number v-model="generateForm.limit" :min="1" placeholder="输入限制条数"></el-input-number>
        </el-form-item> -->

        <!-- 添加 SQL 预览框 -->
        <el-form-item label="SQL预览">
          <el-input
            type="textarea"
            v-model="previewSql"
            :rows="5"
            placeholder="SQL语句预览，可直接编辑"
            @input="handlePreviewSqlInput"
          ></el-input>
          <div v-if="sqlValidationErrors.length > 0" class="sql-validation-errors">
            <p v-for="(error, index) in sqlValidationErrors" :key="index" class="warning-message">
              <i class="el-icon-warning-outline warning-icon"></i> {{ error }}
            </p>
            <p class="notice-message" v-if="forceSubmit">
              <i class="el-icon-info info-icon"></i> 再次点击确认会忽略该提示并保存
            </p>
          </div>
        </el-form-item>
         <!-- 在生成主题弹窗的表单底部添加追加按钮 -->
         <el-form-item>
          <el-button type="primary" @click="handleAppendSql" :disabled="!previewSql">追加SQL</el-button>
        </el-form-item>


        <el-form-item label="备注">
          <el-input type="textarea" v-model="generateForm.remake" :rows="2" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>


      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleGenerateSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 新增 DDS Info 弹窗 -->
    <el-dialog
      :title="ddsInfoDialogTitle"
      :visible.sync="ddsInfoDialogVisible"
      width="60%"
    >
      <el-form :model="ddsInfoForm" :rules="ddsInfoRules" ref="ddsInfoForm" label-width="100px">
        <el-form-item label="发布类型" prop="publishType">
          <el-select
            v-model="ddsInfoForm.publishType"
            placeholder="请选择发布类型"
            @change="handleDdsInfoPublishTypeChange"
          >
            <el-option label="发布" :value="1"></el-option>
            <el-option label="执行" :value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作类型" prop="type">
          <el-select
            v-model="ddsInfoForm.type"
            placeholder="请选择操作类型"
            :disabled="ddsInfoForm.publishType === 1"
          >
            <el-option
              v-for="option in operationTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="ddsInfoForm.publishType === 1 && option.value !== '1'"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="ddsInfoForm.name" placeholder="请输入名称"></el-input>
        </el-form-item>

        <el-form-item label="频次" prop="frequency">
          <el-input-number v-model="ddsInfoForm.frequency" :min="1" placeholder="请输入频次"></el-input-number>
        </el-form-item>

        <el-form-item label="映射SQL" prop="sqlValue">
          <el-input
            type="textarea"
            v-model="ddsInfoForm.sqlValue"
            :rows="4"
            placeholder="请输入SQL语句,多条sql之间用:间隔，参数占位符用'?'"
            @input="handleSqlInput"
          ></el-input>
          <div v-if="sqlValidationErrors.length > 0" class="sql-validation-errors">
            <p v-for="(error, index) in sqlValidationErrors" :key="index" class="warning-message">
              <i class="el-icon-warning-outline warning-icon"></i> {{ error }}
            </p>
            <p class="notice-message" v-if="forceSubmit">
              <i class="el-icon-info info-icon"></i> 再次点击确认会忽略该提示并保存
            </p>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remake">
          <el-input
            type="textarea"
            v-model="ddsInfoForm.remake"
            :rows="2"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="ddsInfoDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDdsInfo">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addShardingConfig, list, makeInUse,deleteById,addDdsInfo,infoList,makeInfoInUse,deleteInfoById } from '@/api/dds'
import {  list as sourceList,getDatabaseNames,getTableNames,getTableColumns } from '@/api/source'
import { getToken } from '@/utils/auth'
import { adduserDds, deleteData, list as userDdsList } from '@/api/userDds'

export default {
  name: 'Index',
  components: {},
  data() {
    return {
      ddsInfoName:'topic',
      ranks:1,
      readOnlyStatus:false,
      addSharding: false,
      modelTitle: '新增配置',
      userDialogVisible: false,
      userLoading: false,
      userData: [],
      currentInfoId: null,
      searchUserId:'',
      userPage: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      ddsConfig:{
        id:'',
        name:'',
        selectedOption: '',
        shardingTextConfig:"",
        tableConfig:"",
        remake:"",
        databaseConfig:"",
      },
      databaseData:'',
      selectedSource: '',
      selectedTable: '',
      centerDialogVisible: false,
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      infoData:[],
      parentValue: '',
      showRightPanel: false,
      infoPage: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      generateDialogVisible: false,
      generateForm: {
        sourceId: '',
        database: '',
        table: '',
        sqlType: '',
        selectedFields: [],
        publishType: 2, // 默认为执行类型
        name: '', // 新增名称字段
        frequency: 10, // 新增频次字段,默认值10
        remake: '', // 新增备注字段
        whereConditions: [], // 新增where条件数组
        groupByFields: [], // 新增分组字段
        orderByFields: [], // 新增排序字段
        limit: '', // 新增分页限制
        useAllFields: false // 新增是否使用所有字段标志
      },
      sourceOptions: [],
      databaseOptions: [],
      tableOptions: [],
      tableFields: [],
      previewSql: '', // 用于显示SQL预览
      appendMode: false,
      originalSql: '',
      sqlTypeOptions: [
        { label: '查询', value: 1 },
        { label: '新增', value: 2 },
        { label: '修改', value: 3 },
        { label: '删除', value: 4 }
      ],
      operationTypeOptions: [
        { label: '查询', value: 1 },
        { label: '新增', value: 2 },
        { label: '修改', value: 3 },
        { label: '删除', value: 4 }
      ],
      ddsInfoDialogVisible: false,
      ddsInfoDialogTitle: '',
      ddsInfoForm: {
        id: '',
        publishType: 2,
        type: '',
        name: '',
        frequency: 10,
        sqlValue: '',
        remake: '',
        configId: ''
      },
      ddsInfoRules: {
        publishType: [
          { required: true, message: '请选择发布类型', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择操作类型', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9]+$/, message: '名称只能包含字母和数字', trigger: 'blur' }
        ],
        frequency: [
          { required: true, message: '请输入频次', trigger: 'blur' }
        ],
        sqlValue: [
          { required: true, message: '请输入SQL语句', trigger: 'blur' }
        ]
      },
      sqlValidationErrors: [], // 存储SQL验证错误信息
      forceSubmit: false, // 是否强制提交标志
    }
  },
  computed: {
    infoOption(){
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 6,
        searchShowBtn: true,
        searchResetBtn: true,
        page: true,
        align: 'center',
        menu: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        menuWidth: 150,
        column: [
          {
            label: 'ID',
            prop: 'id',
            hide: true,
            editDisplay: false,
            addDisplay: false,
            search: true,
            searchSpan: 6,
            searchPlaceholder: '请输入ID'
          },
          {
            label: '发布类型',
            prop: 'publishType',
            type: 'select',
            search: true,
            searchSpan: 6,
            searchPlaceholder: '请选择发布类型',
            width: 100,
            value: 2,
            dicData: [
              {
                label: '发布',
                value: 1
              },
              {
                label: '执行',
                value: 2
              }
            ]
          },
          {
            label: '操作类型',
            prop: 'type',
            type: 'select',
            search: true,
            searchSpan: 6,
            searchPlaceholder: '请选择操作类型',
            width: 100,
            value: '1',
            dicData: [
              { label: '查询', value: 1 },
              { label: '新增', value: 2 },
              { label: '修改', value: 3 },
              { label: '删除', value: 4 }
            ]
          },
          {
            label: '名称',
            prop: 'name',
            width: 120,
            search: true,
            searchSpan: 6,
            searchPlaceholder: '请输入名称'
          },
          {
            label: '状态',
            prop: 'status',
            width: 80,
            slot: 'avue-switch',

            editDisplay: false,
            addDisplay: false
          },
          {
            label: '频次',
            prop: 'frequency',
            type: 'number',
            width: 80
          },
          {
            label: '映射SQL',
            prop: 'sqlValue',
            type: 'textarea',
            span: 24,
            minRows: 3,
            maxRows: 6,
            placeholder: "请输入SQL语句,多条sql之间用:间隔，参数占位符用'?'"
          },
          {
            label: '备注',
            prop: 'remake',
            width: 120,
            type: 'textarea'
          }
        ]
      }
    },
    userOption() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 6,
        searchShow: false,
        page: true,
        align: 'center',
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: '用户ID',
            prop: 'userId',
            search: true,
            searchSpan: 12,
            searchLabelWidth: 70
          },
          {
            label: '用户名称',
            prop: 'userName',
            search: false
          },
          {
            label: '状态',
            prop: 'status',
            slot: true
          }
        ]
      }
    },
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        menuWidth: 200,
        align: 'center',
        menu: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'name',
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '是否指定',
            prop: 'isSpecified',
            type: 'select',
            dicData: [
              {
                label: '否',
                value: 0
              },
              {
                label: '是',
                value: 1
              }
            ],
            editDisplay: false,  // 编辑时不显示
            addDisplay: false    // 新增时不显示
          },
          {
            label: '是否启用',
            prop: 'inUse',
            slot: 'avue-switch'
          },
          {
            label: '备注',
            prop: 'remake',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '修改时间',
            prop: 'updateTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowDel (form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteById({  id:form.id}).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.centerDialogVisible = false;
            this.onLoad(this.page, this.params)
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {  });
    },
    rowInfoDel (form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteInfoById({  id:form.id}).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.infoData = this.infoData.filter(item => item.id !== form.id);
            this.generateConfigInfo();
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {  });
    },
    saveConfig(){
      if (this.readOnlyStatus){
        this.centerDialogVisible = false;
        return
      }
      if (!this.ddsConfig.name){
        this.$message.error('请输入配置名称！')
        return
      }
      var from = {
        id:this.ddsConfig.id,
        name:this.ddsConfig.name,
        remake:this.ddsConfig.remake,
        ddsInfos:this.infoData,
        configInfo:this.ddsConfig.shardingTextConfig,
      }
      addShardingConfig(from).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功！')
          this.centerDialogVisible = false;
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('保存失败！')
        }
      })
    },

    showAuthModal(row) {
      this.currentInfoId = row.id
      this.userDialogVisible = true
      this.loadUserList()
    },

    loadUserList() {
      this.userLoading = true
      userDdsList(this.userPage.pageNo, this.userPage.pageSize, { infoId: this.currentInfoId,userId:this.searchUserId  }).then(res => {
        if (res.code === 200) {
          const data = res.data
          this.userData = data.records
          this.userPage.total = data.total
        }
        this.userLoading = false
      })
    },

    showModal(type,row) {
      console.log(type);
      this.readOnlyStatus = false;
      if (type===1){  // 新增
        this.ranks = 1;
        this.infoData = [];
        this.modelTitle = "新增配置";
        this.ddsConfig.id = null;
        this.ddsConfig.name = null;
        this.ddsConfig.remake = null;
        this.ddsConfig.shardingTextConfig = null;
        this.addSharding = true;
        this.showRightPanel = false;  // 新增时关闭预览
      }else if (type===2){  // 编辑
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {configId:row.id}).then(res => {
          this.infoData = res.data.records;
          this.infoPage.pageNo = res.data.current;
          this.infoPage.pageSize = res.data.size;
          this.infoPage.total = res.data.total;
          this.ranks = res.data.records.length + 1;
          this.modelTitle = "编辑配置";
          this.addSharding = false;
          this.ddsConfig.id = row.id;
          this.ddsConfig.name = row.name;
          this.ddsConfig.remake = row.remake;
          this.ddsConfig.shardingTextConfig = row.configInfo;
          this.showRightPanel = false;  // 编辑时关闭预览
        })
      }else if (type===3){  // 查看
        infoList(null,null,{configId:row.id}).then(res => {
          this.readOnlyStatus = true;
          this.infoData = res.data;
          this.modelTitle = "查看配置";
          this.addSharding = false;
          this.ddsConfig.id = row.id;
          this.ddsConfig.name = row.name;
          this.ddsConfig.remake = row.remake;
          this.ddsConfig.shardingTextConfig = row.configInfo;
          this.showRightPanel = true;  // 查看时打开预览
        })
      }
      this.centerDialogVisible = true;
    },

    switChange(row) {
      makeInUse({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    changeInfoStatus(row){
      makeInfoInUse({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.generateConfigInfo();
          this.$message.success('操作成功')
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    generateConfigInfo(){
      let result = '';
      let count = 1;
      this.infoData.forEach(item => {
        if (item.status === 1) {
          result += `${item.name}:${item.sqlValue}:${item.frequency}\n`;
          count++;
        }
      });
      this.ddsConfig.shardingTextConfig = result;
    },
    rowInfoSave(form, done, loading) {
      form.configId = this.ddsConfig.id;
      this.operationTypeOptions = this.sqlTypeOptions
      // 添加发布类型验证
      if (form.publishType === 1 && form.type !== '1') {
        this.$message.error('发布类型为发布时，操作类型只能为查询')
        done();
        return
      }

      // 当发布类型为发布时，验证SQL类型和WHERE条件
      if (form.publishType === 1) {
        const sqlValue = form.sqlValue.trim().toUpperCase();
        if (!sqlValue.startsWith('SELECT')) {
          this.$message.error('发布类型为发布时，SQL只能是查询语句');
          done();
          return;
        }

        // 验证WHERE条件是否有固定值，不能使用占位符
        if (sqlValue.includes('WHERE')) {
          const whereClause = sqlValue.substring(sqlValue.indexOf('WHERE'));
          if (whereClause.includes('?')) {
            this.$message.error('发布类型为发布时，WHERE条件必须有固定值，不能使用占位符');
            done();
            return;
          }
        }
      }

      const pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        done();
        return
      }

      const pattern1 = /^\d+$/;
      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        done();
        return
      }

      loading()
      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData.push(resp.data)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/;
          const match = form.name.match(topRegex);
          if (match) {
            const number = parseInt(match[1], 10);
            this.ranks = number + 1;
          }
          done()
        } else {
          this.$message.error(resp.msg)
          loading(false)
          done()
        }
      }).catch(() => {
        loading(false)
        done()
      })
    },
    rowInfoUpdate(form, index, done, loading) {
      // 添加发布类型验证
      this.operationTypeOptions = this.sqlTypeOptions

      if (form.publishType === 1 && form.type !== 'SELECT') {
        this.$message.error('发布类型为发布时，操作类型只能为查询')
        done();
        return
      }

      // 当发布类型为发布时，验证SQL类型和WHERE条件
      if (form.publishType === 1) {
        const sqlValue = form.sqlValue.trim().toUpperCase();
        if (!sqlValue.startsWith('SELECT')) {
          this.$message.error('发布类型为发布时，SQL只能是查询语句');
          done();
          return;
        }

        // 验证WHERE条件是否有固定值，不能使用占位符
        if (sqlValue.includes('WHERE')) {
          const whereClause = sqlValue.substring(sqlValue.indexOf('WHERE'));
          if (whereClause.includes('?')) {
            this.$message.error('发布类型为发布时，WHERE条件必须有固定值，不能使用占位符');
            done();
            return;
          }
        }
      }

      const pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        done();
        return
      }

      const pattern1 = /^\d+$/;
      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        done();
        return
      }

      loading()
      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData = this.infoData.filter(item => item.id !== form.id);
          this.infoData.push(resp.data)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/; // 正则表达式，用于匹配 'topic' 开头并跟随数字的字符串
          const match = form.name.match(topRegex); // 匹配字符串

          if (match) {
            const number = parseInt(match[1], 10); // 取出数字部分并转换为整数
            this.ranks = number + 1; // 数字加 1 并返回
          }
          done()
        } else {
          this.$message.error(resp.msg)
          loading(false)
          done()
        }
      }).catch(() => {
        loading(false)
        done()
      })
    },
    searchInfoChange(params, done) {
      if (this.ddsConfig.id) {
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {
          configId: this.ddsConfig.id,
          ...params
        }).then(res => {
          this.infoData = res.data.records;
          this.infoPage.total = res.data.total;
          done();
        });
      } else {
        done();
      }
    },
    searchInfoReset() {
      if (this.ddsConfig.id) {
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {
          configId: this.ddsConfig.id
        }).then(res => {
          this.infoData = res.data.records;
          this.infoPage.total = res.data.total;
        });
      }
    },
    sizeInfoChange(pageSize) {
      this.infoPage.pageNo = 1;
      this.infoPage.pageSize = pageSize;
      if (this.ddsConfig.id) {
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {configId: this.ddsConfig.id}).then(res => {
          this.infoData = res.data.records;
          this.infoPage.total = res.data.total;
        });
      }
    },
    currentInfoChange(pageNo) {
      this.infoPage.pageNo = pageNo;
      if (this.ddsConfig.id) {
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {configId: this.ddsConfig.id}).then(res => {
          this.infoData = res.data.records;
          this.infoPage.total = res.data.total;
        });
      }
    },
    refreshInfoChange() {
      if (this.ddsConfig.id) {
        infoList(this.infoPage.pageNo, this.infoPage.pageSize, {configId: this.ddsConfig.id}).then(res => {
          this.infoData = res.data.records;
          this.infoPage.total = res.data.total;
        });
      }
    },
    searchUserChange(params, done) {
      this.searchUserId = params.userId;
      this.userPage.pageNo=1
      this.loadUserList()
      done()
    },

    changeUserStatus(row) {
      if (row.status === 1) {
        adduserDds({
          userId: row.userId,
          infoId: this.currentInfoId
        }).then(res => {
          if (res.code === 200) {
            this.$message.success('授权成功')
          } else {
            row.status = 0
            this.$message.error(res.msg || '授权失败')
          }
        })
      } else {
        deleteData({
          userId: row.userId,
          infoId: this.currentInfoId
        }).then(res => {
          if (res.code === 200) {
            this.$message.success('取消授权成功')
          } else {
            row.status = 1
            this.$message.error(res.msg || '取消授权失败')
          }
        })
      }
    },
    rowSave(form, done, loading) {
      form.configId = this.ddsConfig.id;
      const pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        done();
        return
      }
      const pattern1 = /^\d+$/;
      // if (!pattern1.test(form.ranks)) {
      //   this.$message.error('序号只能为数字')
      //   return
      // }
      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        done();
        return
      }
      loading()
      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData.push(resp.data)
          console.log(this.infoData)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/; // 正则表达式，用于匹配 'topic' 开头并跟随数字的字符串
          const match = form.name.match(topRegex); // 匹配字符串

          if (match) {
            const number = parseInt(match[1], 10); // 取出数字部分并转换为整数
            this.ranks = number + 1; // 数字加 1 并返回
          }
          done()
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      var pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        return
      }
       const pattern1 = /^\d+$/;

      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        return
      }
      // const regex = /^\s*SELECT\s+/i;
      // if (!regex.test(form.sqlValue)) {
      //   this.$message.error('映射sql只能是查询语句')
      //   return
      // }

      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData = this.infoData.filter(item => item.id !== form.id);
          this.infoData.push(resp.data)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/; // 正则表达式，用于匹配 'topic' 开头并跟随数字的字符串
          const match = form.name.match(topRegex); // 匹配字符串

          if (match) {
            const number = parseInt(match[1], 10); // 取出数字部分并转换为整数
            this.ranks = number + 1; // 数字加 1 并返回
          }
          done()
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },

    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    },
    toggleRightPanel() {
      this.showRightPanel = !this.showRightPanel;
    },
    refreshUserChange() {
      this.searchUserId = ''  // 清空搜索条件
      this.userPage.pageNo = 1  // 重置页码
      this.loadUserList()  // 重新加载列表
    },
    openGenerateModal() {
      // 重置所有表单数据
      this.resetGenerateForm();

      // 重置SQL验证相关状态
      this.sqlValidationErrors = [];
      this.forceSubmit = false;

      // 重置追加模式相关状态
      this.appendMode = false;
      this.originalSql = '';

      // 获取数据源列表
      sourceList(1, 99999).then(res => {
        if (res.code === 200) {
          this.sourceOptions = res.data.records;
          this.generateDialogVisible = true;
        } else {
          this.$message.error("数据源获取失败");
        }
      });
    },

    // 数据源变化处理
    handleSourceChange(sourceId) {
      this.generateForm.database = '';
      this.generateForm.table = '';
      this.generateForm.selectedFields = [];
      // 调用获取数据库列表的接口
      getDatabaseNames({id:sourceId}).then(res => {
        if (res.code === 200) {
          // 将字符串数组转换为选项对象数组
          this.databaseOptions = res.data.map(name => ({
            name: name
          }));
        }
      });
    },

    // 数据库变化处理
    handleDatabaseChange(database) {
      this.generateForm.table = '';
      this.generateForm.selectedFields = [];
      // 调用获取表列表的接口
      getTableNames({id:this.generateForm.sourceId,databaseName:database}).then(res => {
        if (res.code === 200) {
          // 将字符串数组转换为选项对象数组
          this.tableOptions = res.data.map(name => ({
            name: name
          }));
        }
      });
    },

    // 表变化处理
    handleTableChange(table) {
      this.generateForm.selectedFields = [];
      this.previewSql = ''; // 清空 SQL 预览
      // 调用获取字段列表的接口
      getTableColumns({id:this.generateForm.sourceId,databaseName:this.generateForm.database,tableName:table}).then(res => {
        if (res.code === 200) {
          // 直接使用返回的字段数组
          this.tableFields = res.data.map(field => ({
            name: field.name,
            type: field.type
          }));
        }
      });
    },

    // 提交生成
    handleGenerateSubmit() {
      if (!this.validateGenerateForm()) {
        return;
      }

      // 验证SQL - 使用用户可能编辑过的SQL
      const sqlErrors = this.validateSql(this.previewSql);
      this.sqlValidationErrors = sqlErrors;

      if (sqlErrors.length > 0 && !this.forceSubmit) {
        this.forceSubmit = true;
        return;
      }

      // 重置标志
      this.forceSubmit = false;
      this.sqlValidationErrors = [];

      // 当发布类型为发布时，进行额外的验证
      if (this.generateForm.publishType === 1) {
        // 验证是否为查询语句
        const sqlValue = this.previewSql.trim().toUpperCase();
        if (!sqlValue.startsWith('SELECT')) {
          this.$message.error('发布类型为发布时，SQL只能是查询语句');
          return;
        }

        // 验证WHERE条件是否有固定值，不能使用占位符
        if (sqlValue.includes('WHERE')) {
          const whereClause = sqlValue.substring(sqlValue.indexOf('WHERE'));
          if (whereClause.includes('?')) {
            this.$message.error('发布类型为发布时，WHERE条件必须有固定值，不能使用占位符');
            return;
          }
        }
      }

      // 构建新的DDS配置信息 - 使用用户编辑后的SQL
      const newInfo = {
        name: this.generateForm.name,
        publishType: this.generateForm.publishType,
        type: this.generateForm.sqlType,
        frequency: this.generateForm.frequency,
        sqlValue: this.previewSql,
        configId: this.ddsConfig.id,
        remake: this.generateForm.remake
      };

      // 调用添加DDS配置的接口
      addDdsInfo(newInfo).then(resp => {
        if (resp.code === 200) {
          this.$message.success('生成成功');
          this.generateDialogVisible = false;
          this.infoData.push(resp.data);
          this.generateConfigInfo();

          const topRegex = /^topic(\d+)$/;
          const match = newInfo.name.match(topRegex);
          if (match) {
            this.ranks = parseInt(match[1], 10) + 1;
          }
        } else {
          this.$message.error(resp.msg || '生成失败');
        }
      });
    },

    // 添加重置表单方法
    resetGenerateForm() {
      this.generateForm = {
        sourceId: '',
        database: '',
        table: '',
        sqlType: '',
        selectedFields: [],
        publishType: 2, // 这个值会在handlePublishTypeChange中被重新设置
        name: '',
        frequency: 10,
        remake: '',
        whereConditions: [],
        groupByFields: [],
        orderByFields: [],
        limit: '',
        useAllFields: false
      };

      // 清空SQL预览
      this.previewSql = '';

      // 重置其他相关状态
      this.appendMode = false;
      this.originalSql = '';
      this.tableFields = [];
      this.databaseOptions = [];
      this.tableOptions = [];
    },

    // 表单验证
    validateGenerateForm() {
      if (!this.generateForm.name) {
        this.$message.error('请输入名称');
        return false;
      }
      if (!this.generateForm.publishType) {
        this.$message.error('请选择发布类型');
        return false;
      }
      if (!this.generateForm.frequency || this.generateForm.frequency < 1) {
        this.$message.error('请输入有效的频次');
        return false;
      }
      if (!this.generateForm.sourceId) {
        this.$message.error('请选择数据源');
        return false;
      }
      if (!this.generateForm.sqlType) {
        this.$message.error('请选择SQL类型');
        return false;
      }

      // 验证WHERE条件的字段和值是否已填写
      if (this.generateForm.sqlType === 1 && this.generateForm.whereConditions.length > 0) {
        for (let i = 0; i < this.generateForm.whereConditions.length; i++) {
          const condition = this.generateForm.whereConditions[i];
          // 字段始终是必填的
          if (!condition.field) {
            this.$message.error(`第${i+1}个查询条件的字段不能为空`);
            return false;
          }
          // 只有当发布类型为发布时，值必须输入
          if (this.generateForm.publishType === 1 && !condition.value) {
            this.$message.error(`发布类型为发布时，第${i+1}个查询条件的值不能为空`);
            return false;
          }
        }
      }

      return true;
    },

    // 处理全字段选择
    handleAllFieldsChange(value) {
      if (value) {
        this.generateForm.selectedFields = [];
      }
      this.handleFieldsChange();
    },

    // 添加WHERE条件
    addWhereCondition() {
      this.generateForm.whereConditions.push({
        field: '',
        operator: '=',
        value: ''
      });
      this.handleFieldsChange();
    },

    // 移除WHERE条件
    removeWhereCondition(index) {
      this.generateForm.whereConditions.splice(index, 1);
      this.handleFieldsChange();
    },

    // 添加排序字段
    addOrderField() {
      this.generateForm.orderByFields.push({
        field: '',
        direction: 'ASC'
      });
      this.handleFieldsChange();
    },

    // 移除排序字段
    removeOrderField(index) {
      this.generateForm.orderByFields.splice(index, 1);
      this.handleFieldsChange();
    },

    // 修改生成SQL语句的方法
    generateSqlStatement() {
      const { sqlType, table, selectedFields, useAllFields, whereConditions, groupByFields, orderByFields, limit } = this.generateForm;
      let sql = '';

      // 处理字段选择
      const fields = useAllFields ? '*' : selectedFields.map(field => `"${field}"`).join(', ');

      switch (sqlType) {
        case 1: // 查询
          sql = `SELECT ${fields} FROM "${table}"`;

          // 添加WHERE条件，根据发布类型处理值
          if (whereConditions.length > 0) {
            const whereClause = whereConditions
              .map(condition => {
                const value = condition.value?.trim() || '';
                // 当发布类型为发布时，不使用占位符和单引号
                if (this.generateForm.publishType === 1) {
                  if (condition.operator === 'LIKE') {
                    return `"${condition.field}" ${condition.operator} ${value}`; // LIKE 条件
                  } else {
                    return `"${condition.field}" ${condition.operator} ${value}`; // 其他条件
                  }
                }
                // 当发布类型为执行时，使用占位符和单引号
                else {
                  if (condition.operator === 'LIKE') {
                    return `"${condition.field}" ${condition.operator} ${value ? `'${value}'` : "'?'"}`;  // LIKE 条件
                  } else {
                    return `"${condition.field}" ${condition.operator} ${value ? `'${value}'` : "'?'"}`;  // 其他条件
                  }
                }
              })
              .join(' AND ');
            sql += ` WHERE ${whereClause}`;
          }

          // 添加GROUP BY
          if (groupByFields.length > 0) {
            sql += ` GROUP BY ${groupByFields.map(field => `"${field}"`).join(', ')}`;
          }

          // 添加ORDER BY
          if (orderByFields.length > 0) {
            sql += ` ORDER BY ${orderByFields
              .map(order => `"${order.field}" ${order.direction}`)
              .join(', ')}`;
          }

          // 添加LIMIT
          if (limit) {
            sql += ` LIMIT ${limit}`;
          }
          break;
        case 2: // 新增
          sql = `INSERT INTO "${table}" (${selectedFields.map(field => `"${field}"`).join(', ')}) VALUES (${selectedFields.map(() => "'?'").join(', ')})`;
          break;
        case 3: // 修改
          sql = `UPDATE "${table}" SET ${selectedFields.map(field => `"${field}" = '?'`).join(', ')} WHERE "id" = '?'`;
          break;
        case 4: // 删除
          sql = `DELETE FROM "${table}" WHERE "id" = '?'`;
          break;
      }

      return sql;
    },

    // 字段选择变化时触发
    handleFieldsChange() {
      this.sqlValidationErrors = [];
      this.forceSubmit = false;

      const newSql = this.generateSqlStatement();
      if (this.appendMode && this.originalSql) {
        this.previewSql = `${this.originalSql}\n${newSql}`;
      } else {
        this.previewSql = newSql;
      }
    },

    // 修改 SQL 类型变化时也更新 SQL 预览
    handleSqlTypeChange() {
      // 重置useAllFields状态
      this.generateForm.useAllFields = false;

      // 清空SQL预览内容
      this.previewSql = '';

      // 清空相关字段
      this.generateForm.whereConditions = [];
      this.generateForm.groupByFields = [];
      this.generateForm.orderByFields = [];
      this.generateForm.limit = '';

      if (this.generateForm.selectedFields.length > 0) {
        const newSql = this.generateSqlStatement();
        if (this.appendMode && this.originalSql) {
          this.previewSql = `${this.originalSql}\n${newSql}`;
        } else {
          this.previewSql = newSql;
        }
      }
    },

    handleAppendSql() {
      // 保存当前的SQL
      this.originalSql = this.previewSql;

      // 重置表单相关字段
      this.generateForm.table = '';
      if (this.generateForm.publishType===1){
        this.generateForm.sqlType = '1';
      }else {
        this.generateForm.sqlType = '';
      }

      this.generateForm.selectedFields = [];
      this.tableFields = [];

      // 设置追加模式
      this.appendMode = true;
    },

    // 发布类型变化时的处理
    handlePublishTypeChange(value) {
      if (value === 1) {
        // 如果是发布类型，强制设置为查询
        this.generateForm.sqlType = 1;
      }

      // 重置表单和SQL预览
      this.resetGenerateForm();

      // 保持发布类型的选择
      this.generateForm.publishType = value;
      if (value === 1) {
        // 如果是发布类型，强制设置为查询
        this.generateForm.sqlType = 1;
      }
    },

    // 打开新增/编辑弹窗
    openDdsInfoDialog(type, row) {
      this.ddsInfoDialogVisible = true;
      this.ddsInfoDialogTitle = type === 'add' ? '新增DDS配置' : '编辑DDS配置';
      this.sqlValidationErrors = [];
      this.forceSubmit = false;

      if (type === 'edit' && row) {
        this.ddsInfoForm = { ...row };
      } else {
        this.ddsInfoForm = {
          id: '',
          publishType: 2,
          type: '',
          name: '',
          frequency: 10,
          sqlValue: '',
          remake: '',
          configId: this.ddsConfig.id
        };
      }
    },

    // 处理发布类型变化
    handleDdsInfoPublishTypeChange(value) {
      if (value === 1) {
        this.ddsInfoForm.type = 1;
      }
    },

    // 验证SQL语句
    validateSql(sql) {
      const errors = [];

      // 分割多条SQL语句
      const sqlStatements = sql.split(':');

      sqlStatements.forEach((statement, index) => {
        const trimmedSql = statement.trim().toUpperCase();

        // 基本语法检查
        if (!trimmedSql) {
          errors.push(`第${index + 1}条SQL语句为空`);
          return;
        }

        // 检查SQL注释
        if (trimmedSql.includes('--') || trimmedSql.includes('/*') || trimmedSql.includes('*/')) {
          errors.push(`第${index + 1}条SQL语句建议不要包含注释`);
        }

        // 检查是否包含危险的SQL命令
        const dangerousCommands = ['DROP', 'TRUNCATE', 'ALTER', 'GRANT', 'REVOKE'];
        dangerousCommands.forEach(command => {
          if (trimmedSql.includes(command)) {
            errors.push(`第${index + 1}条SQL语句包含危险命令: ${command}`);
          }
        });

        // 检查SQL类型及其基本结构
        if (this.ddsInfoForm.publishType === 1) {
          // 发布类型只能是SELECT
          if (!trimmedSql.startsWith('SELECT')) {
            errors.push(`第${index + 1}条SQL语句建议使用SELECT查询语句`);
          } else {
            // SELECT语句规范检查
            if (trimmedSql.includes('SELECT *')) {
              errors.push(`第${index + 1}条查询语句建议避免使用SELECT *，可以明确指定需要的查询字段`);
            }
            if (!trimmedSql.includes('FROM')) {
              errors.push(`第${index + 1}条SELECT语句需要包含FROM子句`);
            }
            // 检查GROUP BY后是否有聚合函数
            if (trimmedSql.includes('GROUP BY') &&
                !/(COUNT|SUM|AVG|MAX|MIN)\s*\(/i.test(trimmedSql)) {
              errors.push(`第${index + 1}条SQL的GROUP BY子句建议配合聚合函数使用`);
            }
          }
        } else {
          // 执行类型的SQL语法检查
          if (trimmedSql.startsWith('SELECT')) {
            // SELECT语句检查
            if (!trimmedSql.includes('FROM')) {
              errors.push(`第${index + 1}条SELECT语句需要包含FROM子句`);
            }
            if (trimmedSql.includes('SELECT *')) {
              errors.push(`第${index + 1}条查询语句建议避免使用SELECT *，可以明确指定需要的查询字段`);
            }
          } else if (trimmedSql.startsWith('INSERT')) {
            // INSERT语句检查
            if (!trimmedSql.includes('INTO')) {
              errors.push(`第${index + 1}条INSERT语句需要包含INTO关键字`);
            }
            if (!trimmedSql.includes('VALUES') && !trimmedSql.includes('SELECT')) {
              errors.push(`第${index + 1}条INSERT语句需要包含VALUES子句或SELECT子句`);
            }
            // 检查INSERT字段与VALUES是否对应
            const insertMatch = trimmedSql.match(/INSERT\s+INTO\s+\w+\s*\((.*?)\)\s*VALUES\s*\((.*?)\)/i);
            if (insertMatch) {
              const fields = insertMatch[1].split(',').length;
              const values = insertMatch[2].split(',').length;
              if (fields !== values) {
                errors.push(`第${index + 1}条INSERT语句的字段数量应与VALUES数量一致`);
              }
            }
          } else if (trimmedSql.startsWith('UPDATE')) {
            // UPDATE语句检查
            if (!trimmedSql.includes('SET')) {
              errors.push(`第${index + 1}条UPDATE语句需要包含SET子句`);
            }
            if (!trimmedSql.includes('WHERE')) {
              errors.push(`第${index + 1}条UPDATE语句建议包含WHERE条件，避免全表更新`);
            }
          } else if (trimmedSql.startsWith('DELETE')) {
            // DELETE语句检查
            if (!trimmedSql.includes('FROM')) {
              errors.push(`第${index + 1}条DELETE语句需要包含FROM子句`);
            }
            if (!trimmedSql.includes('WHERE')) {
              errors.push(`第${index + 1}条DELETE语句建议包含WHERE条件，避免全表删除`);
            }
          } else {
            errors.push(`第${index + 1}条SQL语句建议以SELECT/INSERT/UPDATE/DELETE开头`);
          }
        }

        // 检查参数占位符 - 改进的逻辑，当参数为固定值时不需要占位符
        const placeholderCount = (statement.match(/\?/g) || []).length;

        // 检查是否需要占位符
        let needsPlaceholder = false;

        // 检查WHERE子句
        if (trimmedSql.includes('WHERE')) {
          // 如果WHERE子句中没有具体的值（字符串、数字等），则需要占位符
          const whereClause = trimmedSql.substring(trimmedSql.indexOf('WHERE'));
          // 检查是否有字符串值（单引号或双引号包围）
          const hasStringValues = /['"][^'"]*['"]/.test(whereClause);
          // 检查是否有数字值
          const hasNumberValues = /\s+\d+\s*(?:AND|OR|$)/.test(whereClause);

          // 如果没有具体的值，则需要占位符
          if (!hasStringValues && !hasNumberValues) {
            needsPlaceholder = true;
          }
        }

        // 检查VALUES子句
        if (trimmedSql.includes('VALUES')) {
          // 如果VALUES子句中没有具体的值（字符串、数字等），则需要占位符
          const valuesClause = trimmedSql.substring(trimmedSql.indexOf('VALUES'));
          // 检查是否有字符串值（单引号或双引号包围）
          const hasStringValues = /['"][^'"]*['"]/.test(valuesClause);
          // 检查是否有数字值
          const hasNumberValues = /\(\s*\d+/.test(valuesClause);

          // 如果没有具体的值，则需要占位符
          if (!hasStringValues && !hasNumberValues) {
            needsPlaceholder = true;
          }
        }

        // 如果需要占位符但没有使用，则显示错误
        if (needsPlaceholder && placeholderCount === 0) {
          errors.push(`第${index + 1}条SQL语句建议使用参数占位符(?)`);
        }

        // 检查WHERE子句的条件规范
        if (trimmedSql.includes('WHERE')) {
          // 检查是否有1=1这样的永真条件
          if (/WHERE\s+[\w\s]*?[=><]\s*\1/.test(trimmedSql)) {
            errors.push(`第${index + 1}条SQL语句WHERE子句包含永真条件，建议检查`);
          }
          // 检查是否在索引字段上使用函数
          if (/WHERE\s+\w+\s*\(.*?\)/.test(trimmedSql)) {
            errors.push(`第${index + 1}条SQL语句WHERE子句中不建议在字段上使用函数`);
          }
        }

        // 检查JOIN语句规范
        if (trimmedSql.includes('JOIN')) {
          if (!trimmedSql.includes('ON')) {
            errors.push(`第${index + 1}条SQL语句的JOIN需要包含ON条件`);
          }
          // 检查是否使用了NATURAL JOIN
          if (trimmedSql.includes('NATURAL JOIN')) {
            errors.push(`第${index + 1}条SQL语句不建议使用NATURAL JOIN`);
          }
        }

        // 检查ORDER BY规范
        if (trimmedSql.includes('ORDER BY')) {
          if (trimmedSql.includes('ORDER BY RAND()')) {
            errors.push(`第${index + 1}条SQL语句不建议使用ORDER BY RAND()`);
          }
        }

        // 增加对SELECT语句的详细检查
        if (trimmedSql.startsWith('SELECT')) {
          // GROUP BY 子句检查
          if (trimmedSql.includes('GROUP BY')) {
            // 检查GROUP BY是否有配套的聚合函数
            if (!/(COUNT|SUM|AVG|MAX|MIN)\s*\(/i.test(trimmedSql)) {
              errors.push(`第${index + 1}条SQL的GROUP BY子句应配合聚合函数使用`);
            }

            // 检查HAVING子句是否正确使用
            if (trimmedSql.includes('HAVING')) {
              if (!/(COUNT|SUM|AVG|MAX|MIN)\s*\(/i.test(trimmedSql.substring(trimmedSql.indexOf('HAVING')))) {
                errors.push(`第${index + 1}条SQL的HAVING子句建议使用聚合函数`);
              }
            }

            // 检查GROUP BY字段是否都在SELECT中出现
            const groupByMatch = trimmedSql.match(/GROUP\s+BY\s+([^;]+?)(?:\s+HAVING|\s+ORDER|\s+LIMIT|$)/i);
            if (groupByMatch) {
              const groupByFields = groupByMatch[1].split(',').map(f => f.trim());
              const selectClause = trimmedSql.substring(
                trimmedSql.indexOf('SELECT') + 6,
                trimmedSql.indexOf('FROM')
              ).trim();

              groupByFields.forEach(field => {
                if (!selectClause.includes(field) && !field.includes('(')) {
                  errors.push(`第${index + 1}条SQL的GROUP BY字段 ${field} 建议在SELECT子句中包含`);
                }
              });
            }
          }

          // ORDER BY 子句检查
          if (trimmedSql.includes('ORDER BY')) {
            // 检查ORDER BY是否使用了不推荐的函数
            if (trimmedSql.includes('ORDER BY RAND()')) {
              errors.push(`第${index + 1}条SQL语句不建议使用ORDER BY RAND()`);
            }

            // 检查ORDER BY后是否指定了排序方向
            const orderByMatch = trimmedSql.match(/ORDER\s+BY\s+([^;]+?)(?:\s+LIMIT|$)/i);
            if (orderByMatch) {
              const orderByFields = orderByMatch[1].split(',');
              orderByFields.forEach(field => {
                if (!/(ASC|DESC)$/i.test(field.trim())) {
                  errors.push(`第${index + 1}条SQL的ORDER BY子句建议明确指定排序方向(ASC/DESC)`);
                }
              });
            }

            // 如果有DISTINCT，检查ORDER BY的字段是否都在SELECT中
            if (trimmedSql.includes('DISTINCT')) {
              const selectFields = trimmedSql
                .substring(trimmedSql.indexOf('DISTINCT') + 8, trimmedSql.indexOf('FROM'))
                .split(',')
                .map(f => f.trim());

              const orderByFields = orderByMatch ? orderByMatch[1]
                .split(',')
                .map(f => f.trim().replace(/(ASC|DESC)$/i, '').trim()) : [];

              orderByFields.forEach(field => {
                if (!selectFields.includes(field)) {
                  errors.push(`第${index + 1}条SQL使用DISTINCT时，建议ORDER BY字段出现在SELECT列表中`);
                }
              });
            }
          }

          // LIMIT 子句检查
          if (trimmedSql.includes('LIMIT')) {
            // 检查LIMIT语法
            const limitMatch = trimmedSql.match(/LIMIT\s+(\d+)(?:\s*,\s*(\d+))?$/i);
            if (limitMatch) {
              const offset = limitMatch[2];
              const limit = limitMatch[1];

              // 检查LIMIT值的合理性
              if (parseInt(limit) <= 0) {
                errors.push(`第${index + 1}条SQL的LIMIT子句数值建议大于0`);
              }

              // 如果使用了OFFSET语法，建议使用LIMIT x OFFSET y的形式
              if (offset) {
                errors.push(`第${index + 1}条SQL建议使用LIMIT x OFFSET y替代LIMIT y,x的写法`);
              }

              // 检查是否有ORDER BY
              if (!trimmedSql.includes('ORDER BY')) {
                errors.push(`第${index + 1}条SQL使用LIMIT时建议配合ORDER BY使用，可以使结果更加确定`);
              }
            }
          }

          // 子查询检查
          if (trimmedSql.includes('(SELECT')) {
            // 检查子查询是否有别名
            const subqueryMatches = trimmedSql.match(/\(\s*SELECT[^)]+\)/g);
            if (subqueryMatches) {
              subqueryMatches.forEach(subquery => {
                if (!/ AS \w+/i.test(trimmedSql.substring(trimmedSql.indexOf(subquery) + subquery.length))) {
                  errors.push(`第${index + 1}条SQL的子查询建议指定别名，提高可读性`);
                }
              });
            }
          }
        }
      });

      return errors;
    },

    // 添加新方法处理SQL输入
    handleSqlInput() {
      // 当SQL被修改时，重置校验状态
      this.sqlValidationErrors = [];
      this.forceSubmit = false;
    },

    // 处理SQL预览输入
    handlePreviewSqlInput() {
      // 当预览SQL被直接编辑时，重置校验状态
      this.sqlValidationErrors = [];
      this.forceSubmit = false;
    },

    submitDdsInfo() {
      this.$refs.ddsInfoForm.validate((valid) => {
        if (valid) {
          // 验证SQL
          const sqlErrors = this.validateSql(this.ddsInfoForm.sqlValue);
          this.sqlValidationErrors = sqlErrors;

          if (sqlErrors.length > 0 && !this.forceSubmit) {
            this.forceSubmit = true;
            return;
          }

          // 重置标志
          this.forceSubmit = false;
          this.sqlValidationErrors = [];

          // 当发布类型为发布时，进行额外的验证
          if (this.ddsInfoForm.publishType === 1) {
            // 验证是否为查询语句
            const sqlValue = this.ddsInfoForm.sqlValue.trim().toUpperCase();
            if (!sqlValue.startsWith('SELECT')) {
              this.$message.error('发布类型为发布时，SQL只能是查询语句');
              return;
            }

            // 验证WHERE条件是否有固定值，不能使用占位符
            if (sqlValue.includes('WHERE')) {
              const whereClause = sqlValue.substring(sqlValue.indexOf('WHERE'));
              if (whereClause.includes('?')) {
                this.$message.error('发布类型为发布时，WHERE条件必须有固定值，不能使用占位符');
                return;
              }
            }
          }

          // 继续提交操作
          addDdsInfo(this.ddsInfoForm).then(resp => {
            if (resp.code === 200) {
              this.$message.success('操作成功');
              this.ddsInfoDialogVisible = false;

              if (this.ddsInfoForm.id) {
                this.infoData = this.infoData.filter(item => item.id !== this.ddsInfoForm.id);
              }
              this.infoData.push(resp.data);
              this.generateConfigInfo();

              const topRegex = /^topic(\d+)$/;
              const match = this.ddsInfoForm.name.match(topRegex);
              if (match) {
                this.ranks = parseInt(match[1], 10) + 1;
              }
            } else {
              this.$message.error(resp.msg || '操作失败');
            }
          });
        }
      });
    }
  },
  watch: {
    'generateForm.whereConditions': {
      handler() {
        this.handleFieldsChange();
      },
      deep: true
    },
    'generateForm.groupByFields': {
      handler() {
        this.handleFieldsChange();
      },
      deep: true
    },
    'generateForm.orderByFields': {
      handler() {
        this.handleFieldsChange();
      },
      deep: true
    },
    'generateForm.limit'() {
      this.handleFieldsChange();
    },
    'generateForm.selectedFields': {
      handler() {
        this.handleFieldsChange();
      },
      deep: true
    }
  }
}
</script>
<style scoped>
.grid-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}

.button-row {
  margin-top: 20px;
}

.ep-bg-purple {
  background: #d3dce6;
}

.ep-bg-purple-light {
  background: #e9eef3;
}

.add {
  width: 100%;
  height: 30px;
  color: #3396fa;
  text-align: center;
  border-top: 1px solid #ccc;
  font-size: 14px;
  line-height: 30px;
  position: sticky;
  bottom: 0px;
  background-color: #fff;
  opacity: 1;
}
.delete-option {
  float: right;
  color: #3396fa;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 5%;
}
.border {
  border: 1px solid #c0ccda; /* 使用 Element UI 提供的默认边框颜色 */
  padding: 10px; /* 内边距 */
}
.dds-table-container {
  max-height: 450px; /* 设置最大高度 */
  overflow-y: auto; /* 超出部分启用纵向滚动条 */
}
.toggle-panel-btn {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}
.sql-validation-errors {
  margin-top: 5px;
  padding: 5px;
}

.warning-message {
  color: #e6a23c;  /* Element UI 的警告色 */
  font-size: 12px;
  margin-top: 5px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.notice-message {
  color: #409EFF;  /* Element UI 的主要蓝色 */
  font-size: 12px;
  margin-top: 5px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.warning-icon {
  color: #e6a23c;  /* Element UI 的警告色 */
  font-size: 16px;
  margin-right: 5px;
}

.info-icon {
  color: #409EFF;  /* Element UI 的主要蓝色 */
  font-size: 16px;
  margin-right: 5px;
}

.where-condition {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.order-condition {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}
</style>
