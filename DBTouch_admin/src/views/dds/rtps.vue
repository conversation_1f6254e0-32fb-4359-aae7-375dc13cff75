<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['dds:config:addBtn']" type="primary" icon="el-icon-plus" size="small"
                   @click="showModal(1)"
        >新 增
        </el-button>
      </template>
      <template #inUse="scope">
        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="switChange(scope.row)"
        ></el-switch>
      </template>
      <template #menu="{row,index}">
        <el-button v-hasPermi="['dds:config:editBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-edit"
                   @click="showModal(2,row)">编辑</el-button>
        <el-button v-hasPermi="['dds:config:lookBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-view"
                   @click="showModal(3,row)">查看</el-button>
        <el-button v-hasPermi="['dds:config:delBtn']"
                   type="text"
                   size="mini"
                   icon="el-icon-delete"
                   @click="rowDel(row,index)">删除</el-button>
      </template>
    </avue-crud>

    <el-dialog
      :visible.sync="centerDialogVisible"
      :title="modelTitle"
      width="80%"
      align-center
    >
      <el-row>
        <!-- 左侧部分 -->
        <el-col :span="12">
          <div class="grid-content">
            <div class="form-item">
              <span class="form-label">配置名称：</span>
              <el-input  v-model="ddsConfig.name"  :disabled="readOnlyStatus" placeholder="请输入配置名称"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">备注：</span>
              <el-input type="textarea" v-model="ddsConfig.remake" :disabled="readOnlyStatus" :rows="2" placeholder="请输入备注"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">SPDP发送地址：</span>
              <el-input  v-model="ddsConfig.spdpSendAddress" @change="generateConfigInfo"  :disabled="readOnlyStatus" placeholder="请输入SPDP发送地址"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">SPDP本地地址：</span>
              <el-input  v-model="ddsConfig.spdpLocalAddress" @change="generateConfigInfo"  :disabled="readOnlyStatus" placeholder="请输入SPDP本地地址"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">SEDP本地地址：</span>
              <el-input  v-model="ddsConfig.sedpLocalAddress" @change="generateConfigInfo"  :disabled="readOnlyStatus" placeholder="请输入SEDP本地地址"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">域编号：</span>
              <el-input  v-model="ddsConfig.domainId" @change="generateConfigInfo" :disabled="readOnlyStatus"  placeholder="请输入域编号"></el-input>
            </div>
          </div>
        </el-col>

        <!-- 右侧部分 -->
        <el-col :span="12">
          <div class="grid-content">
            <span class="form-label">配置文件预览：</span>
            <el-input type="textarea" v-model="ddsConfig.shardingTextConfig" :disabled="true" :rows="23" placeholder="配置文件内容"></el-input>
          </div>
        </el-col>
      </el-row>
      <template #footer>

        <div class="dialog-footer">
          <div v-if="readOnlyStatus">
            <el-button @click="centerDialogVisible = false">关 闭</el-button>
          </div>
          <div v-else>
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveConfig">
              确 认
            </el-button>
          </div>

        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { addRtpsConfig, rtpsConfigList, makeInUseRtpsConfig,deleteRtpsConfigById } from '@/api/dds'
import {  list as sourceList } from '@/api/source'
import { getToken } from '@/utils/auth'

export default {
  name: 'Index',
  components: {},
  data() {
    return {
      ddsInfoName:'topic',
      ranks:1,
      readOnlyStatus:false,
      addSharding: false,
      modelTitle: '新增配置',
      ddsConfig:{
        id:'',
        name:'',
        spdpSendAddress: '',
        shardingTextConfig:"",
        spdpLocalAddress:"",
        sedpLocalAddress:"",
        domainId:"",
        remake:"",
        databaseConfig:"",
      },
      databaseData:'',
      selectedSource: '',
      selectedTable: '',
      centerDialogVisible: false,
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      infoData:[],
      parentValue: ''
    }
  },
  computed: {
    infoOption(){
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: false,
        align: 'center',
        menu: true,
        addBtn: true,
        editBtn: true,
        delBtn: true,
        selection: false,

        column: [
          {
            label: '名称',
            prop: 'name',
            value: this.ddsInfoName+this.ranks,
            editDisplay: true,
            addDisplay: true
          },
          // {
          //   label: '序号',
          //   prop: 'ranks',
          //   type:'number',
          //   value: this.ranks,
          //   editDisplay: true,
          //   addDisplay: true
          // },
          {
            label: '映射sql',
            prop: 'sqlValue',
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '频次',
            prop: 'frequency',
            type:'number',
            value:10,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '是否启用',
            prop: 'inUse',
            editDisplay: false,
            addDisplay: false,
            slot: 'avue-switch'
          },
          {
            label: '备注',
            prop: 'remake',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
        ]
      }
    },
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        menuWidth: 200,
        align: 'center',
        menu: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'name',
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '是否启用',
            prop: 'inUse',
            slot: 'avue-switch'
          },

          {
            label: '备注',
            prop: 'remake',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '修改时间',
            prop: 'updateTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowDel (form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRtpsConfigById({  id:form.id}).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.centerDialogVisible = false;
            this.onLoad(this.page, this.params)
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {  });
    },
    rowInfoDel (form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteInfoById({  id:form.id}).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.infoData = this.infoData.filter(item => item.id !== form.id);
            this.generateConfigInfo();
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {  });
    },
    saveConfig(){
      if (this.readOnlyStatus){
        this.centerDialogVisible = false;
        return
      }
      if (!this.ddsConfig.name){
        this.$message.error('请输入配置名称！')
        return
      }
      if (!this.ddsConfig.shardingTextConfig){
        this.$message.error('请输入spdp发送地址！')
        return
      }
      if (!this.ddsConfig.spdpLocalAddress){
        this.$message.error('请输入spdp本地地址！')
        return
      }
      if (!this.ddsConfig.sedpLocalAddress){
        this.$message.error('请输入sedp本地地址！')
        return
      }
      if (!this.ddsConfig.domainId){
        this.$message.error('请输入域编号！')
        return
      }
      var from = {
        id:this.ddsConfig.id,
        name:this.ddsConfig.name,
        remake:this.ddsConfig.remake,
        spdpSendAddress:this.ddsConfig.spdpSendAddress,
        spdpLocalAddress:this.ddsConfig.spdpLocalAddress,
        sedpLocalAddress:this.ddsConfig.sedpLocalAddress,
        domainId:this.ddsConfig.domainId,
        configInfo:this.ddsConfig.shardingTextConfig,
      }
      addRtpsConfig(from).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功！')
          this.centerDialogVisible = false;
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('保存失败！')
        }
      })
    },

    showModal(type,row) {
      console.log(type);
      this.readOnlyStatus = false;
      if (type===1){
        this.ranks = 1;
        this.infoData = [];
        this.modelTitle = "新增配置";
        this.ddsConfig.id = null;
        this.ddsConfig.name = null;
        this.ddsConfig.remake = null;
        this.ddsConfig.spdpSendAddress = null;
        this.ddsConfig.spdpLocalAddress = null;
        this.ddsConfig.sedpLocalAddress = null;
        this.ddsConfig.domainId = null;
        this.ddsConfig.shardingTextConfig = null;
        this.addSharding = true
      }else if (type===2){
        this.modelTitle = "编辑配置";
        this.addSharding = false
        this.ddsConfig.id = row.id;
        this.ddsConfig.name = row.name;
        this.ddsConfig.remake = row.remake;
        this.ddsConfig.spdpSendAddress = row.spdpSendAddress;
        this.ddsConfig.spdpLocalAddress = row.spdpLocalAddress;
        this.ddsConfig.sedpLocalAddress = row.sedpLocalAddress;
        this.ddsConfig.domainId = row.domainId;
        this.ddsConfig.shardingTextConfig = row.configInfo;

      }else if (type===3){
        this.readOnlyStatus = true
        this.ddsConfig.id = row.id;
        this.ddsConfig.name = row.name;
        this.ddsConfig.remake = row.remake;
        this.ddsConfig.spdpSendAddress = row.spdpSendAddress;
        this.ddsConfig.spdpLocalAddress = row.spdpLocalAddress;
        this.ddsConfig.sedpLocalAddress = row.sedpLocalAddress;
        this.ddsConfig.domainId = row.domainId;
        this.ddsConfig.shardingTextConfig = row.configInfo;
      }
      this.centerDialogVisible = true
    },

    switChange(row) {
      makeInUseRtpsConfig({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    changeInfoStatus(row){
      makeInfoInUse({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.generateConfigInfo();
          this.$message.success('操作成功')
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    generateConfigInfo() {
      const domainId = this.ddsConfig.domainId;
      const spdpSendAddress = this.ddsConfig.spdpSendAddress;
      const spdpLocalAddress = this.ddsConfig.spdpLocalAddress ;
      const sedpLocalAddress = this.ddsConfig.sedpLocalAddress ;

      this.ddsConfig.shardingTextConfig = `[common]
DCPSGlobalTransportConfig=$file
ORBDebugLevel=0
DCPSDebugLevel=10
DCPSTransportDebugLevel=0
ORBLogFile=publisher.log

[domain/${domainId}]
DiscoveryConfig=fast_rtps

[rtps_discovery/fast_rtps]
ResendPeriod=2

SpdpSendAddrs=${spdpSendAddress}
SpdpLocalAddress=${spdpLocalAddress}

SedpAdvertisedLocalAddress=${sedpLocalAddress}
##PB=30000
##DX=0
##D1=0
SedpMulticast=0
SedpLocalAddress=${sedpLocalAddress}
TTL=20

[transport/the_rtps_transport]
transport_type=rtps_udp
use_multicast=0
local_address=${sedpLocalAddress}
ttl=20
    `
    },
    rowSave(form, done, loading) {
      form.configId = this.ddsConfig.id;
      const pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        return
      }
      const pattern1 = /^\d+$/;
      // if (!pattern1.test(form.ranks)) {
      //   this.$message.error('序号只能为数字')
      //   return
      // }
      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        return
      }
      loading()
      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData.push(resp.data)
          console.log(this.infoData)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/; // 正则表达式，用于匹配 'topic' 开头并跟随数字的字符串
          const match = form.name.match(topRegex); // 匹配字符串

          if (match) {
            const number = parseInt(match[1], 10); // 取出数字部分并转换为整数
            this.ranks = number + 1; // 数字加 1 并返回
          }
          done()
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      var pattern = /^[a-zA-Z0-9]+$/
      if (!pattern.test(form.name)) {
        this.$message.error('名称必须由字母或数字组成')
        return
      }
       const pattern1 = /^\d+$/;

      if (!pattern1.test(form.frequency)) {
        this.$message.error('频次只能为数字')
        return
      }
      const regex = /^\s*SELECT\s+/i;
      if (!regex.test(form.sqlValue)) {
        this.$message.error('映射sql只能是查询语句')
        return
      }

      addDdsInfo(form).then(resp => {
        if (resp.code === 200) {
          this.infoData = this.infoData.filter(item => item.id !== form.id);
          this.infoData.push(resp.data)
          this.generateConfigInfo()
          this.$message.success('操作成功')
          const topRegex = /^topic(\d+)$/; // 正则表达式，用于匹配 'topic' 开头并跟随数字的字符串
          const match = form.name.match(topRegex); // 匹配字符串

          if (match) {
            const number = parseInt(match[1], 10); // 取出数字部分并转换为整数
            this.ranks = number + 1; // 数字加 1 并返回
          }
          done()
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      rtpsConfigList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },

    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
<style scoped>
.grid-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}

.button-row {
  margin-top: 20px;
}

.ep-bg-purple {
  background: #d3dce6;
}

.ep-bg-purple-light {
  background: #e9eef3;
}

.add {
  width: 100%;
  height: 30px;
  color: #3396fa;
  text-align: center;
  border-top: 1px solid #ccc;
  font-size: 14px;
  line-height: 30px;
  position: sticky;
  bottom: 0px;
  background-color: #fff;
  opacity: 1;
}
.delete-option {
  float: right;
  color: #3396fa;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 5%;
}
.border {
  border: 1px solid #c0ccda; /* 使用 Element UI 提供的默认边框颜色 */
  padding: 10px; /* 内边距 */
}
.dds-table-container {
  max-height: 450px; /* 设置最大高度 */
  overflow-y: auto; /* 超出部分启用纵向滚动条 */
}
</style>
