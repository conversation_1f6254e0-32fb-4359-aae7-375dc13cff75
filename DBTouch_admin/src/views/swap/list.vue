<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
    <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['business:swap:export']" type="warning" icon="el-icon-download" size="mini"
                   @click="handleExport">导出</el-button>
      </template>
    <template slot="userId" slot-scope="scope">
        <div>
          <i v-if="scope.row.whitelistStatus == 1" style="color: #F56C6C;" class="el-icon-lock" title="白名单，限制中"></i>
          <i v-if="scope.row.whitelistStatus == 2" style="color: #67C23A;" class="el-icon-unlock" title="白名单，不做限制"></i>
          {{ scope.row.userId }}
        </div>
      </template>
    </avue-crud>
  </div>
</template>
<script>
import { getList,getExport } from '@/api/swap'

export default {
  name: 'Swap',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: []
    }
  },
  computed: {
    option() {
      return {
        selection: false,
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        searchLabelWidth:100,
        searchLabelPosition:'left',
        column: [
          {
            label: 'ID',
            prop: 'id',
            disabled: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '用户id',
            prop: 'userId',
            disabled: false,
            addDisplay: true,
            editDisplay: false,
            search: true,
            searchSpan: 4
          },
          {
            label: '滑点比例',
            prop: 'slipRate',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '价格',
            prop: 'price',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '数量',
            prop: 'amount',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '申请币种',
            prop: 'submitCurrency',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '实际到账数量',
            prop: 'receiveAmount',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '兑换币种',
            prop: 'currency',
            disabled: false,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            disabled: false,
            addDisplay: false,
            editDisplay: false,
            search: false,
            dicData: [
              {
                label: '不成功',
                value: 0
              },
              {
                label: '成功',
                value: 1
              }
            ]
          },
          {
            label: '创建时间',
            prop: 'createTime',
            editDisplay: false,
            addDisplay: false,
            disabled: true
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },
    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('swap/export', {
        ...this.query
      }, `swap_${new Date().getTime()}.xlsx`)
    }
  },
   
}
</script>
