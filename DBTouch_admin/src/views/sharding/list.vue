<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['sharding:config:addBtn']" type="primary" icon="el-icon-plus" size="small"
                   @click="showModal(1)"
        >新 增
        </el-button>
      </template>
      <template #inUse="scope">
        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="switChange(scope.row)"
        ></el-switch>
      </template>
      <template #menu="{row,index}">
        <el-button type="text"
                   size="mini"
                   icon="el-icon-edit"
                   @click="showModal(2,row)">编辑</el-button>
        <el-button type="text"
                   size="mini"
                   icon="el-icon-view"
                   @click="showModal(3,row)">查看</el-button>
        <el-button type="text"
                   size="mini"
                   icon="el-icon-delete"
                   @click="rowDel(row,index)">删除</el-button>
      </template>
    </avue-crud>

    <el-dialog v-dialogDrag
      :visible.sync="centerDialogVisible"
      :title="modelTitle"
      width="80%"
      align-center
    >
      <el-row>
        <!-- 左侧部分 -->
        <el-col v-show="isZoomIn" :span="12">
          <div class="grid-content">
            <div class="form-item">
              <span class="form-label">配置名称：</span>
              <el-input  v-model="shardingConfig.name"  :disabled="readOnlyStatus" placeholder="请输入配置名称"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">备注：</span>
              <el-input type="textarea" v-model="shardingConfig.remake" :disabled="readOnlyStatus" :rows="1" placeholder="请输入备注"></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">数据源：</span>
              <el-select v-model="shardingConfig.selectedOption" :disabled="readOnlyStatus" multiple placeholder="请选择数据源" @change="changeDataBase" style="width: 100%;">
                <el-option v-for="(item, index) in databaseData" :key="item.id" :label="item.name"
                           :value="item.id">
                  <template slot-scope="scope">
                    <span>{{ item.name }}</span>
                    <span v-if="item.isNew" class="new-icon">NEW</span>
                  </template>
                </el-option>

              </el-select>
            </div>
<!--            <div class="form-item">-->
<!--              <span class="form-label">分表规则：</span>-->
<!--              <el-input type="textarea" v-model="shardingConfig.tableConfig" :disabled="readOnlyStatus" @change="changeDataBase" :rows="4" placeholder="请输入分表规则"></el-input>-->
<!--            </div>-->
<!--            <div class="form-item">-->
<!--              <span class="form-label">分库规则：</span>-->
<!--              <el-input type="textarea" v-model="shardingConfig.sourceConfig" :disabled="readOnlyStatus"-->
<!--                        @change="changeDataBase" :rows="4" placeholder="请输入分库规则"-->
<!--              />-->
<!--            </div>-->
<!--            <div class="form-item">-->
<!--              <span class="form-label">主从设置：</span>-->
<!--              <el-input type="textarea" v-model="shardingConfig.masterSlaveConfig" :disabled="readOnlyStatus" @change="changeDataBase" :rows="4" placeholder="请输入分库规则">-->
<!--              </el-input>-->
<!--            </div>-->
            <el-tabs v-model="activeTab">
              <el-tab-pane label="分表规则" name="tableRule">
                <div class="form-item">
                  <el-button
                    class="expand-button"
                    :icon="isExpanded ? 'el-icon-zoom-out' : 'el-icon-zoom-in'"
                    @click="toggleExpand"
                  />
                  <el-input
                    type="textarea"
                    v-model="shardingConfig.tableConfig"
                    :disabled="readOnlyStatus"
                    @change="changeDataBase"
                    :rows="isExpanded ? 20 : 10"
                    placeholder="请输入分表规则"
                  ></el-input>
                </div>
              </el-tab-pane>
              <el-tab-pane label="分库规则" name="dbRule">
                <div class="form-item">
                  <el-button
                    class="expand-button"
                    :icon="isExpanded ? 'el-icon-zoom-out' : 'el-icon-zoom-in'"
                    @click="toggleExpand"
                  />
                  <el-input
                    type="textarea"
                    v-model="shardingConfig.sourceConfig"
                    :disabled="readOnlyStatus"
                    @change="changeDataBase"
                    :rows="isExpanded ? 20 : 10"
                    placeholder="请输入分库规则"
                  />
                </div>
              </el-tab-pane>
              <el-tab-pane label="主从设置" name="masterSlave">
                <div class="form-item">
                  <el-button
                    class="expand-button"
                    :icon="isExpanded ? 'el-icon-zoom-out' : 'el-icon-zoom-in'"
                    @click="toggleExpand"
                  />
                  <el-input
                    type="textarea"
                    v-model="shardingConfig.masterSlaveConfig"
                    :disabled="readOnlyStatus"
                    @change="changeDataBase"
                    :rows="isExpanded ? 20 : 10"
                    placeholder="请输入主从设置"
                  ></el-input>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>

<!--        <el-col v-show="!isZoomIn" :span="12">-->
<!--          <div class="grid-content">-->
<!--            <div class="input-container">-->
<!--              <span class="form-label" >{{zoomOut.label}}</span>-->
<!--              <el-button-->
<!--                class="expand-button"-->
<!--                icon="el-icon-zoom-out"-->
<!--                @click="toggleExpand"-->
<!--              ></el-button>-->
<!--            </div>-->

<!--            <el-input type="textarea" v-model="zoomOut.textArea" :rows="23" :placeholder="zoomOut.placeholder"></el-input>-->
<!--          </div>-->
<!--        </el-col>-->
        <!-- 右侧部分 -->
        <el-col :span="12">
          <div class="grid-content">
            <span class="form-label">配置文件内容：</span>
            <el-input type="textarea"
                      v-model="shardingConfig.shardingTextConfig"
                      :disabled="true"
                      :rows="isExpanded ? 34 : 24"
                      placeholder="配置文件内容"></el-input>
          </div>
        </el-col>
      </el-row>
      <template #footer>

        <div class="dialog-footer">
          <div v-if="readOnlyStatus">
            <el-button @click="centerDialogVisible = false">关 闭</el-button>
          </div>
          <div v-else>
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveConfig">
              确 认
            </el-button>
          </div>

        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { addShardingConfig, list, makeInUse,deleteById } from '@/api/shard'
import {  list as sourceList } from '@/api/source'

export default {
  name: 'Index',
  components: {
  },
  data() {
    return {
      activeTab: 'tableRule', // 默认激活的标签页
      isZoomIn:true,
      isExpanded:false,
      editorSize: 'small',
      readOnlyStatus:false,
      addSharding: false,
      modelTitle: '新增配置',
      shardingConfig:{
        id:'',
        name:'',
        selectedOption: '',
        shardingTextConfig:"",
        tableConfig:"",
        remake:"",
        databaseConfig:"",
        sourceConfig:"",
        masterSlaveConfig:"",
      },
      editorOptions: {
        selectOnLineNumbers: true,
        automaticLayout: true
      },
      databaseData:'',
      selectedSource: '',
      selectedTable: '',
      centerDialogVisible: false,
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue: ''
    }
  },
  computed: {
    editorContainerStyle() {
      return {
        height: this.editorSize === 'small' ? '200px' : '400px',
        width: '100%'
      };
    },
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        menuWidth: 200,
        align: 'center',
        menu: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'shardName',
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '是否启用',
            prop: 'inUse',
            slot: 'avue-switch'
          },

          {
            label: '备注',
            prop: 'note',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '修改时间',
            prop: 'updateTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          }
        ]
      }
    }
  },
  created() {
    this.getDatabaseList()
    this.onLoad(this.page, this.params)
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },

    rowDel (form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteById({  id:form.id}).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.centerDialogVisible = false;
            this.onLoad(this.page, this.params)
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {  });
    },
    saveConfig(){
      if (this.readOnlyStatus){
        this.centerDialogVisible = false;
        return
      }
      if (!this.shardingConfig.name){
        this.$message.error('请输入配置名称！')
        return
      }
      var from = {
        id:this.shardingConfig.id,
        shardName:this.shardingConfig.name,
        note:this.shardingConfig.remake,
        tableConfig:this.shardingConfig.tableConfig,
        sourceConfig:this.shardingConfig.sourceConfig,
        masterSlaveConfig:this.shardingConfig.masterSlaveConfig,
        configInfo:this.shardingConfig.shardingTextConfig,
        databaseConfig:this.shardingConfig.databaseConfig,
        allDatabase: this.databaseData.map(item => item.id).join(','),
        databaseIds:this.shardingConfig.selectedOption.join(", ")
      }
      if (!from.databaseIds){
        this.$message.error('请选择数据源！')
        return
      }
      console.log(from);
      addShardingConfig(from).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功！')
          this.centerDialogVisible = false;
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('保存失败！')
        }
      })
    },
    showModal(type,row) {
      this.activeTab="tableRule",
      this.readOnlyStatus = false;
      if (type===1){
        this.modelTitle = "新增配置";
        this.shardingConfig.id = null;
        this.shardingConfig.name = null;
        this.shardingConfig.remake = null;
        this.shardingConfig.tableConfig = null;
        this.shardingConfig.sourceConfig = null;
        this.shardingConfig.databaseConfig = null;
        this.shardingConfig.masterSlaveConfig = null;
        this.shardingConfig.shardingTextConfig = null;
        this.shardingConfig.selectedOption = [];
        this.addSharding = true
        this.databaseData.forEach(item => {item.isNew = false;item.isUpdate = false;});
      }else if (type===2){
        this.modelTitle = "编辑配置";
        this.addSharding = false;
        this.shardingConfig.id = row.id;
        this.shardingConfig.name = row.shardName;
        this.shardingConfig.databaseConfig = row.databaseConfig;
        this.shardingConfig.remake = row.note;
        this.shardingConfig.tableConfig = row.tableConfig;
        this.shardingConfig.sourceConfig = row.sourceConfig;
        this.shardingConfig.masterSlaveConfig = row.masterSlaveConfig;
        this.shardingConfig.shardingTextConfig = row.configInfo;
        this.shardingConfig.selectedOption = row.databaseIds ? row.databaseIds.split(",").map(Number) : [];
        this.databaseData.forEach(item => {
          item.isNew = item.createTime > row.updateTime
          item.isUpdate = item.updateTime > item.createTime
        })
        console.log(this.databaseData)
      }else if (type===3){
        this.modelTitle = "查看配置";
        this.readOnlyStatus = true;
        this.shardingConfig.id = row.id;
        this.shardingConfig.name = row.shardName;
        this.shardingConfig.remake = row.note;
        this.shardingConfig.tableConfig = row.tableConfig;
        this.shardingConfig.sourceConfig = row.sourceConfig;
        this.shardingConfig.masterSlaveConfig = row.masterSlaveConfig;
        this.shardingConfig.shardingTextConfig = row.configInfo;
        this.shardingConfig.selectedOption = row.databaseIds ? row.databaseIds.split(",").map(Number) : [];

      }
      this.centerDialogVisible = true
    },
    // changeSelect(selectedItems){
    //   this.shardingConfig.selectedOption = selectedItems;
    //   console.log(selectedItems)
    // },
    changeDataBase(){
      console.log(this.shardingConfig.tableConfig)
      let index = 0;
      let databaseConfig='';
      this.shardingConfig.selectedOption.forEach(item => {
          this.databaseData.forEach(dataSource => {
            if (item === dataSource.id){
              index++;
              const dsName = 'ds' + index
              let dataType=''
              let schema = ""
              databaseConfig += `  ${dsName}: !!com.zaxxer.hikari.HikariDataSource\n`
              if (dataSource.type === 1) {
                console.log(dataSource)
                dataType = 'dm'
                schema = "schema="+dataSource.databaseName
                databaseConfig += `    driverClassName: dm.jdbc.driver.DmDriver\n`
              } else if (dataSource.type === 2) {
                dataType = 'kingbase8'
                schema = "currentSchema=module"
                databaseConfig += `    driverClassName: com.kingbase8.Driver\n`
              }
              databaseConfig += `    jdbcUrl: jdbc:`+dataType+`://${dataSource.url}:${dataSource.port}/${dataSource.databaseName}?`+schema+`\n`
              databaseConfig += `    username: ${dataSource.username}\n`
              databaseConfig += `    password: ${dataSource.password}\n`
            }
          })
        }
      )
      // this.databaseData.forEach(dataSource => {
      //   if (this.shardingConfig.selectedOption.includes(dataSource.id)) {
      //     index++;
      //     const dsName = 'ds' + index
      //     let dataType=''
      //     let schema = ""
      //     databaseConfig += `  ${dsName}: !!com.zaxxer.hikari.HikariDataSource\n`
      //     if (dataSource.type === 1) {
      //       console.log(dataSource)
      //       dataType = 'dm'
      //       schema = "schema="+dataSource.databaseName
      //       databaseConfig += `    driverClassName: dm.jdbc.driver.DmDriver\n`
      //     } else if (dataSource.type === 2) {
      //       dataType = 'kingbase8'
      //       schema = "currentSchema=module"
      //       databaseConfig += `    driverClassName: com.kingbase8.Driver\n`
      //     }
      //     databaseConfig += `    jdbcUrl: jdbc:`+dataType+`://${dataSource.url}:${dataSource.port}/${dataSource.databaseName}?`+schema+`\n`
      //     databaseConfig += `    username: ${dataSource.username}\n`
      //     databaseConfig += `    password: ${dataSource.password}\n`
      //   }
      // });
      this.shardingConfig.databaseConfig = databaseConfig;
      let textConfig = 'dataSources:\n';
      textConfig += databaseConfig
      this.shardingConfig.tableConfig = this.shardingConfig.tableConfig ? this.shardingConfig.tableConfig.trim():'';
      this.shardingConfig.sourceConfig = this.shardingConfig.sourceConfig ?this.shardingConfig.sourceConfig.trim():'';
      if (this.shardingConfig.tableConfig || this.shardingConfig.sourceConfig) {
        textConfig += 'shardingRule:\n';
      }
      if (this.shardingConfig.tableConfig) {
        // textConfig += '  tables:\n';
        textConfig += `  `;
        textConfig +=this.shardingConfig.tableConfig;
        textConfig +="\n";
      }

      if (this.shardingConfig.sourceConfig) {
        // textConfig += `  defaultDatabaseStrategy:\n`;
        textConfig += `  `;
        textConfig += this.shardingConfig.sourceConfig;
        textConfig +="\n";
      }

      this.shardingConfig.masterSlaveConfig = this.shardingConfig.masterSlaveConfig?this.shardingConfig.masterSlaveConfig.trim():'';
      if (this.shardingConfig.masterSlaveConfig){
        textConfig += 'masterSlaveRule:\n';
        textConfig += `  `;
        textConfig += this.shardingConfig.masterSlaveConfig;
        textConfig +="\n";
      }
      textConfig += 'props:\n' +
        '  sql.show: true';
      this.shardingConfig.shardingTextConfig = textConfig;
    },
    getDatabaseList(){
      sourceList({pageNo: 1, pageSize: 100000}).then(resp => {
        if (resp.code === 200) {
          this.databaseData = resp.data.records
        } else {
          this.$message.error('数据源数据获取失败')
        }
      })
    },

    switChange(row) {
      makeInUse({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    rowSave(form, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },


    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  },

}
</script>
<style scoped>
.grid-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}

.button-row {
  margin-top: 20px;
}

.ep-bg-purple {
  background: #d3dce6;
}

.ep-bg-purple-light {
  background: #e9eef3;
}

.add {
  width: 100%;
  height: 30px;
  color: #3396fa;
  text-align: center;
  border-top: 1px solid #ccc;
  font-size: 14px;
  line-height: 30px;
  position: sticky;
  bottom: 0px;
  background-color: #fff;
  opacity: 1;
}
.delete-option {
  float: right;
  color: #3396fa;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 5%;
}

.new-icon {
  display: inline-block;
  margin-left: 8px;
  padding: 1px 4px;
  border: 1px solid #2196F3;
  border-radius: 5px;
  color: #2196F3;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  background-color: #E3F2FD;
  transform: scale(0.6); /* 缩小图标尺寸至 60% */
  transform-origin: left; /* 保持缩放后的位置 */
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.expand-button {
  background: transparent;
  transform: scale(0.8); /* 缩小图标尺寸至 60% */
  margin-left: 89%;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: inline-block;
  width: 100px;
}
</style>
