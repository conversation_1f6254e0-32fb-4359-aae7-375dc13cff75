<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">
<!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
<!--                 @click="handleExport">导出</el-button>-->
    </template>
    </avue-crud>



  </div>
</template>
<script>
import { getVipLevel,addVip } from '@/api/config'
import { getToken } from '@/utils/auth'
export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue:''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: true,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: false,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'name',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '简介',
            prop: 'description',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '内容',
            prop: 'content',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '价格',
            prop: 'price',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '等级',
            prop: 'level',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '已售数量',
            prop: 'soldAmount',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: 'token',
            prop: 'token',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '退单数量',
            prop: 'returnAmount',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '价格2',
            prop: 'price2',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '描述2',
            prop: 'decription2',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '天数',
            prop: 'days',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '类型',
            prop: 'type',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: 'gpt3数量',
            prop: 'gpt3Count',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: 'gpt4数量',
            prop: 'gpt4Count',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          // {
          //   label: '类型',
          //   prop: 'type',
          //   type: 'select',
          //   search: true,
          //   searchSpan: 4,
          //   editDisplay: true,
          //   addDisplay: true,
          //   //1=天卡,2=次卡,3=级别卡
          //   dicData: [
          //     {
          //       label: '天卡',
          //       value: 1
          //     },
          //     {
          //       label: '次卡',
          //       value: 2
          //     },
          //     {
          //       label: '级别卡',
          //       value: 3
          //     }
          //   ],
          // },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '禁用',
                value: 0
              },
              {
                label: '启用',
                value: 1
              }
            ],
          },

        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowSave(form, done, loading) {
      loading()
      addVip(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addVip(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id){
      this.$router.push('order?capitalId='+id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getVipLevel(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('card/code/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
