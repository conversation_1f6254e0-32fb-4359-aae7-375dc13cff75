<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">
<!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
<!--                 @click="handleExport">导出</el-button>-->
    </template>
    </avue-crud>



  </div>
</template>
<script>
import {getTaskList,addTask} from '@/api/config'
import {getToken} from '@/utils/auth'

export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue: ''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: true,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'name',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '图片',
            prop: 'img',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            search: true,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '暂停',
                value: 0
              },
              {
                label: '运行',
                value: 1
              }
            ],
          },
          {
            label: '介绍',
            prop: 'intro',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '积分',
            prop: 'point',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '排名',
            prop: 'ranks',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '消息',
            prop: 'msg',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '类型',
            prop: 'type',
            type: 'select',
            search: true,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '固定',
                value: 0
              },
              {
                label: '比例',
                value: 1
              }
            ],
          },
          {
            label: '比例',
            prop: 'percentage',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '最大天数',
            prop: 'maxDay',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowSave(form, done, loading) {
      loading()
      addTask(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addTask(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getTaskList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('card/code/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
