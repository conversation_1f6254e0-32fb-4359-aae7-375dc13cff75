<!--<template>
  <div class="app-container">
    <avue-crud ref="crud" :data="data"
               :option="option"
               :table-loading="loading"
               @row-save="rowSave"
               @row-update="rowEdit"
               @refresh-change="initConfig"
               :permission="permission"
    >
    </avue-crud>
  </div>
</template>-->
// <script>
// import { getConfigList, updateConfig } from '@/api/lease'

// export default {
//   data() {
//     return {
//       data: [],
//       permission: {}
//     }
//   },
//   computed: {
//     option() {
//       return {
//         border: true,
//         stripe: true,
//         showHeader: true,
//         size: 'small',
//         // page: true,
//         align: 'center',
//         menuAlign: 'center',
//         addBtn: true,
//         delBtn: false,
//         menu: true,
//         editBtn: true,
//         labelWidth: 150,
//         column: [
//           {
//             label: 'ID',
//             prop: 'id',
//             editDisplay: false,
//             addDisplay: false
//           },
//           {
//             label: '天数',
//             prop: 'day'
//           },
//           {
//             label: '每份收益',
//             prop: 'income'
//           },
//           {
//             label: '类型',
//             prop: 'level',
//             type: 'select',
//             dicData: [
//               {
//                 label: '算力节点',
//                 value: 0
//               },
//               {
//                 label: '社区合伙人',
//                 value: 1
//               }
//             ]
//           },
//           {
//             label: '自动质押',
//             prop: 'autoLease',
//             type: 'select',
//             dicData: [
//               {
//                 label: '关',
//                 value: 0
//               },
//               {
//                 label: '开',
//                 value: 1
//               }
//             ]
//           },
//           {
//             label: '状态开关',
//             prop: 'status',
//             type: 'select',
//             dicData: [
//               {
//                 label: '关',
//                 value: 0
//               },
//               {
//                 label: '开',
//                 value: 1
//               }
//             ]
//           }
//         ]
//       }
//     }
//   },
//   created() {
//     this.initConfig()
//     this.setPermission()
//   },

//   methods: {
//     setPermission() {
//       const value = ['business:leaseConfig:edit']
//       const all_permission = '*:*:*'
//       const permissions = this.$store.getters && this.$store.getters.permissions
//       if (value && value instanceof Array && value.length > 0) {
//         const permissionFlag = value
//         const hasPermissions = permissions.some(permission => {
//           return all_permission === permission || permissionFlag.includes(permission)
//         })
//         this.permission = {
//           editBtn: hasPermissions
//         }
//       }
//     },
//     initConfig() {
//       this.loadding = true
//       getConfigList().then(resp => {
//         this.data = resp.data
//         this.loadding = false
//       })
//     },
//     refreshChange() {
//       this.loadding = true
//       getConfigList().then(resp => {
//         this.data = resp.data
//         this.loadding = false
//       })
//     },
//     rowSave(form, done, loading) {
//       loading()
//       updateConfig(form).then(resp => {
//         if (resp.code === 200) {
//           this.$message.success('操作成功')
//           done(form)
//           this.initConfig()
//         } else {
//           this.$message.error(resp.msg)
//         }
//       })
//     },
//     rowEdit(form, index, done, loading) {
//       loading = true
//       updateConfig(form).then(resp => {
//         if (resp.code === 200) {
//           this.$message.success('操作成功')
//           done(form)
//           this.initConfig()
//         } else {
//           this.$message.error(resp.msg)
//         }
//         loading = false
//       })
//     }
//   }
// }
// </script>
