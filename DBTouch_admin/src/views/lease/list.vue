<!--<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @row-update="rowEdit"
      @refresh-change="refreshChange"
      @current-change="currentChange"-->
    >
<!--      <template slot="menuLeft" slot-scope="scope">
        <el-button type="primary" size="small" @click="outputClick()">产出定时任务</el-button>
        <el-button type="primary" size="small" @click="leaseClick()">出租定时任务</el-button>
      </template>-->
<!--
        <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['business:lease:export']" type="warning" icon="el-icon-download" size="mini"
                   @click="handleExport">导出</el-button>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button
          v-hasPermi="['business:lease:incomeList']"
          type="text"
          icon="eye"
          size="small"
          @click="openRecordDialog(scope.row)"
        >
          查看收益记录
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="收益列表" :visible.sync="dialogRecordVisible">
      <el-table :data="record" style="margin: 0 20px">
        <el-table-column property="id" label="id" width="80"/>
        <el-table-column property="userId" label="用户Id" width="80"/>
        <el-table-column property="amount" label="数量"/>
        <el-table-column property="currency" label="币种"/>
        <el-table-column property="times" label="份数"/>
        <el-table-column property="createTime" label="时间" width="200"/>
      </el-table>
    </el-dialog>
  </div>
</template>
-->
// <script>
// import { getLeaseList, getLeaseIncomeList, leaseReward, output, updateLease } from '@/api/lease'

// export default {
//   name: 'Lease',
//   components: {},
//   data() {
//     return {
//       form: {},
//       lazy: true,
//       loading: true,
//       page: {
//         pageNo: 1,
//         pageSize: 10,
//         total: 10
//       },
//       dialogRecordVisible: false,
//       record: [],
//       data: []
//     }
//   },
//   computed: {
//     option() {
//       return {
//         selection: false,
//         excelBtn: false,
//         loading: true,
//         border: true,
//         stripe: true,
//         showHeader: true,
//         searchMenuSpan: 4,
//         page: true,
//         align: 'center',
//         menu: true,
//         addBtn: false,
//         editBtn: true,
//         delBtn: false,
//         searchLabelWidth:100,
//         searchLabelPosition:'left',
//         column: [
//           {
//             label: 'ID',
//             prop: 'id',
//             disabled: true,
//             addDisplay: false,
//             editDisplay: false,
//           },
//           {
//             label: '用户id',
//             prop: 'userId',
//             disabled: false,
//             addDisplay: true,
//             editDisplay: false,
//             search: true,
//             searchSpan: 4
//           },
//           {
//             label: '份数',
//             prop: 'number',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '租赁状态',
//             prop: 'status',
//             type: 'select',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: true,
//             search: true,
//             searchSpan: 4,
//             dicData: [
//               {
//                 label: '已结束',
//                 value: 0
//               },
//               {
//                 label: '进行中',
//                 value: 1
//               }
//             ]
//           },
//           {
//             label: '类型',
//             prop: 'level',
//             type: 'select',
//             search: true,
//             searchSpan: 4,
//             dicData: [
//               {
//                 label: '算力节点',
//                 value: 0
//               },
//               {
//                 label: '社区合伙人',
//                 value: 1
//               }
//             ]
//           },
//           {
//             label: '是否续租',
//             prop: 'autoLease',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false,
//             dicData: [
//               {
//                 label: '关',
//                 value: 0
//               },
//               {
//                 label: '开',
//                 value: 1
//               }
//             ]
//           },
//           {
//             label: '总收益',
//             prop: 'totalIncome',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '租赁天数',
//             prop: 'day',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '创建时间',
//             prop: 'createTime',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '出租时间',
//             prop: 'beginTime',
//             type: 'datetime',
//             format: 'yyyy-MM-dd HH:mm:ss',
//             valueFormat: 'yyyy-MM-dd HH:mm:ss',
//             editDisplay: false,
//             addDisplay: false,
//             disabled: false
//           },
//           {
//             label: '开始时间',
//             type: 'datetime',
//             prop: 'startTime',
//             format: 'yyyy-MM-dd HH:mm:ss',
//             valueFormat: 'yyyy-MM-dd HH:mm:ss',
//             search: true,
//             searchSpan: 4,
//             viewDisplay: false,
//             editDisplay: false,
//             hide: true
//           },
//           {
//             label: '结束时间',
//             type: 'datetime',
//             prop: 'endTime',
//             format: 'yyyy-MM-dd HH:mm:ss',
//             valueFormat: 'yyyy-MM-dd HH:mm:ss',
//             search: true,
//             searchSpan: 4,
//             viewDisplay: false,
//             editDisplay: false,
//             hide: true
//           }
//         ]
//       }
//     }
//   },
//   created() {
//     this.onLoad(this.page, this.params)
//   },
//   methods: {
//     searchReset() {
//       this.query = {}
//       this.onLoad(this.page)
//     },
//     searchChange(params, done) {
//       this.query = params
//       this.page.pageNo = 1
//       this.onLoad(this.page, params)
//       done()
//     },
//     currentChange(pageNo) {
//       this.page.pageNo = pageNo
//       this.onLoad(this.page)
//     },
//     sizeChange(pageSize) {
//       this.page.pageNo = 1
//       this.page.pageSize = pageSize
//       this.onLoad(this.page)
//     },
//     refreshChange() {
//       this.onLoad(this.page, this.query)
//     },
//     onLoad(page, params = {}) {
//       this.loading = true
//       getLeaseList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
//         const data = res.data
//         this.data = data.records
//         this.page.pageNo = data.current
//         this.page.pageSize = data.size
//         this.page.total = data.total
//         this.loading = false
//       })
//     },
//     openRecordDialog(row) {
//       this.dialogRecordVisible = true
//       this.record = []
//       console.log(row)
//       getLeaseIncomeList({id:row.id, pageNo:1,pageSize:100}).then(resp => {
//         this.record = resp.data.records
//       })
//     },
//     rowEdit(form, index, done, loading) {
//       loading = true
//       updateLease(form).then(resp => {
//         if (resp.code === 200) {
//           this.$message.success('操作成功')
//           done(form)
//           this.onLoad(this.page)
//         } else {
//           this.$message.error(resp.msg)
//         }
//         loading = false
//       })
//     },
//     outputClick() {
//       output().then(resp => {
//         this.$message.success(resp.msg)
//       })
//     },
//     leaseClick() {
//       leaseReward().then(resp => {
//         this.$message.success(resp.msg)
//       })
//     },
//     handleExport() {
//       this.download('lease/export', {
//         ...this.query
//       }, `lease_${new Date().getTime()}.xlsx`)
//     }
//   }
// }
// </script>
