<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
        <!--                 @click="handleExport">导出</el-button>-->
      </template>
    </avue-crud>


  </div>
</template>
<script>
import {productList, addProduct} from '@/api/product'
import {getToken} from '@/utils/auth'

export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue: ''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: true,
        addBtn: true,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: false,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '产品名称',
            prop: 'name',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '产品token',
            prop: 'token',
            search: true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '作品图片',
            prop: 'img',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '作者',
            prop: 'author',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '作者介绍',
            prop: 'authorBref',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '作品介绍',
            prop: 'intro',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '初始价格',
            prop: 'price',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '藏品类型',
            prop: 'productType',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '0',
                value: 0
              },
              {
                label: '1',
                value: 1
              }
            ],
          },
          {
            label: '产品形态',
            prop: 'productForm',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '0',
                value: 0
              },
              {
                label: '1',
                value: 1
              }
            ],
          },
          {
            label: 'CDK类型',
            prop: 'cdkType',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '0',
                value: 0
              },
              {
                label: '1',
                value: 1
              }
            ],
          },
          {
            label: '销售类型',
            prop: 'sellType',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '0',
                value: 0
              },
              {
                label: '1',
                value: 1
              }
            ],
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            search: false,
            searchSpan: 2,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '0',
                value: 0
              },
              {
                label: '1',
                value: 1
              }
            ],
          },
          {
            label: '发售总量',
            prop: 'issueTotal',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '销售数量',
            prop: 'soldAmount',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: 'number'
          },
          {
            label: '发售合作方',
            prop: 'partner',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: 'hash',
            prop: 'hash',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '领取条件',
            prop: 'getCondition',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '排序',
            prop: 'rankIndex',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '注册Token',
            prop: 'regToken',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '商店ID',
            prop: 'storeId',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '排列方式',
            prop: 'arrange',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '代码系列',
            prop: 'codeSerie',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '合作方Token',
            prop: 'partnerToken',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '领取规则',
            prop: 'collectRules',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },

          {
            label: '发行方介绍',
            prop: 'partnerIntro',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '权益赋能',
            prop: 'equity',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: 'IOS价格',
            prop: 'priceIos',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },
          {
            label: 'IOS产品ID',
            prop: 'productidIos',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '隐私权名称',
            prop: 'privliageName',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '退单数量',
            prop: 'returnAmount',
            search: false,
            editDisplay: false,
            addDisplay: false,
            type: "number"
          },
          {
            label: '限购数量',
            prop: 'sellLimit',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },
          {
            label: '临时订单数量',
            prop: 'lockedNum',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },
          {
            label: '权益JSON',
            prop: 'equityJson',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '介绍',
            prop: 'detail',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },

          {
            label: '下载状态',
            prop: 'downloadStatus',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },


          {
            label: '下载方式',
            prop: 'downloadType',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '按钮文字',
            prop: 'buttonText',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '下载状态',
            prop: 'downloadStatus',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '下载链接',
            prop: 'download',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '权益描述',
            prop: 'equityDetail',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '日限量',
            prop: 'dayNum',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '实际日限量',
            prop: 'dayNumReal',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '是否可作为头像',
            prop: 'isHeadimg',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '视频链接',
            prop: 'videoUrl',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '上线状态',
            prop: 'isOnline',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '排序',
            prop: 'sort',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '第二排序',
            prop: 'sort2',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '显示发售总量',
            prop: 'issueTotalDisplay',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },{
            label: '应用令牌',
            prop: 'appToken',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: 'CDK分享令牌',
            prop: 'cdkShareToken',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '预售状态',
            prop: 'presellStatus',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },
          {
            label: '限量JSON',
            prop: 'numJson',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: 'OS限制',
            prop: 'OS限制',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },{
            label: 'SVIP限购数量',
            prop: 'sellLimitSvip',
            search: false,
            editDisplay: true,
            addDisplay: true,
            type: "number"
          },
          {
            label: '状态文字',
            prop: 'statusText',
            search: false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '创建时间',
            prop: 'createTime',
            editDisplay: false,
            addDisplay: false,
            disabled: true,
            width: 130,
          },
          {
            label: '作品创作时间',
            prop: 'composeTime',
            editDisplay: false,
            addDisplay: false,
            disabled: true,
            width: 130,
          },
          {
            label: '销售开始时间',
            prop: 'startTime',
            editDisplay: false,
            addDisplay: false,
            disabled: true,
            width: 130,
          },
          {
            label: '销售结束时间',
            prop: 'endTime',
            editDisplay: false,
            addDisplay: false,
            disabled: true,
            width: 130,
          },
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowSave(form, done, loading) {
      loading()
      addProduct(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addProduct(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      productList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('card/code/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
