<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">
      <el-button v-hasPermi="['dbManager:source:refreshedLog']" type="primary" size="small"
                 @click="refreshed()">刷新日志</el-button>
<!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
<!--                 @click="handleExport">导出</el-button>-->
    </template>

    </avue-crud>



  </div>
</template>
<script>
import { addSource, list,refreshedLog } from '@/api/audit'
import { getToken } from '@/utils/auth'
import { testDataConnect } from '@/api/source'
export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue:''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            width: 120,
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '操作',
            prop: 'action',
            searchSpan: 4,
            width: 120,
            search: true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '数据库',
            prop: 'linkDatabase',
            searchSpan: 4,
            search: true,
            width: 120,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '执行人',
            prop: 'userName',
            width: 120,
            search: true,
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '执行内容',
            prop: 'executeSql',
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '执行时间',
            prop: 'timestamp',
            width: 180,
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowSave(form, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },
    refreshed(){
      refreshedLog().then(resp => {
        if (resp.code === 200) {
          this.$message.success('刷新成功！')
          this.onLoad(this.page)
        } else {
          this.$message.error('刷新失败')
        }
      })
    },
    openOrder(id){
      this.$router.push('order?capitalId='+id)
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
