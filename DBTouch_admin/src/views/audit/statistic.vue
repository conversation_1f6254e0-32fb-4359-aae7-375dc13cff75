<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">
<!--      <el-button v-hasPermi="['dbManager:source:refreshedLog']" type="primary" size="small"-->
<!--                 @click="refreshed()">刷新日志</el-button>-->
<!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
<!--                 @click="handleExport">导出</el-button>-->
    </template>

    </avue-crud>

    <div>
      <div class="charts-container">
        <div ref="pieChart" class="chart" />
        <div ref="histogramChart" class="chart" />
        <div ref="lineChart" class="chart" />
      </div>
    </div>

  </div>
</template>
<script>
import { addSource, statisticList,statisticChart } from '@/api/audit'
import * as echarts from "echarts";
import { getToken } from '@/utils/auth'
import { testDataConnect } from '@/api/source'
export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue:''
    }
  },
  computed: {
    option() {
      return {
        pieData: null,
        lineData: null,
        histogramData: null,
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          // {
          //   label: 'ID',
          //   prop: 'id',
          //   width: 120,
          //   search: true,
          //   searchSpan: 4,
          //   editDisplay: false,
          //   addDisplay: false,
          //   searchBtnText: 'number'
          // },
          {
            label: '日期',
            prop: 'statisticDate',
            search:false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '数据库名',
            prop: 'databaseName',
            search:true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '表名',
            prop: 'tableName',
            search:true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '操作类型',
            prop: 'actionType',
            type: 'select',
            search: true,
            searchSpan: 4,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '查询',
                value: 'select'
              },
              {
                label: '增加',
                value: 'insert'
              },
              {
                label: '删除',
                value: 'delete'
              },
              {
                label: '修改',
                value: 'update'
              },
              {
                label: '其他',
                value: 'unknown'
              }
            ],
          },
          {
            label: '执行次数',
            prop: 'frequency',
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      statisticList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })

      statisticChart(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.pieData = this.transformDataForPieChart(data.pieData);
        this.lineData = this.transformDataForLineChart(data.lineData);
        this.initPieChart(this.pieData);
        this.initLineChart(data.lineData);
        this.initHistogram(data.histogramData);
      })
    },

    transformDataForPieChart(data) {
      const transformed = [];
      for (const [action, users] of Object.entries(data)) {
        for (const [user, count] of Object.entries(users)) {
          transformed.push({
            name: `${user} - ${action}`,
            value: count
          });
        }
      }
      return transformed;
    },
    transformDataForLineChart(data) {
      const transformed = [];
      for (const [date, users] of Object.entries(data)) {
        for (const [user, counts] of Object.entries(users)) {
          transformed.push({
            name: user,
            type: 'line',
            data: counts
          });
        }
      }
      return transformed;
    },
    transformDataForHistogramChart(data) {
      const transformed = [];
      for (const [table, users] of Object.entries(data)) {
        for (const [user, count] of Object.entries(users)) {
          transformed.push({
            name: user,
            type: 'bar',
            data: [count] // assuming a single value per user per table
          });
        }
      }
      return transformed;
    },
    initPieChart(pieData) {
      const myChart = echarts.init(this.$refs.pieChart);
      const option = {
        title: {
          text: '操作类型',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          left: 'left',
          top: '10px'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
        },
        series: [
          {
            name: '操作类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#af0d0d',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',  // 将标签位置改为外部显示
              formatter: '{b} ({d}%)'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true,  // 显示标签线
              length: 15,   // 标签线长度
              lineStyle: {
                width: 1
              }
            },
            data: pieData
          }
        ]
      };
      myChart.setOption(option);
    },
    initLineChart(lineData) {
      const myChart = echarts.init(this.$refs.lineChart);

      // Extract dates and all users from lineData
      const dates = Object.keys(lineData).sort();
      const allUsers = Array.from(new Set(dates.flatMap(date => Object.keys(lineData[date]))));

      const option = {
        title: {
          text: '每日操作统计',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          left: 'left',
          top: '10px'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: '5%',
          left: 'center',
          data: allUsers
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: allUsers.map(user => ({
          name: user,
          type: 'line',
          data: dates.map(date => lineData[date][user] || 0) // Handle missing data
        }))
      };

      myChart.setOption(option);
    },
    initHistogram(histogramData) {
      const myChart = echarts.init(this.$refs.histogramChart);

      // Extract tables and users
      const tables = Object.keys(histogramData);
      const users = Array.from(new Set(tables.flatMap(table => Object.keys(histogramData[table]))));

      const option = {
        title: {
          text: '表格操作统计',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          top: '10px',  // 调整标题位置，位于图表上方一点
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: users,
          top: '10%',  // 调整图例位置，避免与标题重叠
          left: 'center'
        },
        grid: {
          top: '20%',  // 调整图表位置，避免与标题重叠
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: tables
        },
        yAxis: {
          type: 'value'
        },
        series: users.map(user => ({
          name: user,
          type: 'bar',
          stack: '总量',
          data: tables.map(table => histogramData[table][user] || 0)
        }))
      };

      myChart.setOption(option);
    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
<style scoped>
.charts-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.chart {
  flex: 1 1 calc(45% - 20px);
  margin: 10px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 400px;
}

</style>
