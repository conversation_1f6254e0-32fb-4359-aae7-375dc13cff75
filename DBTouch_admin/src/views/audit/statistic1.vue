<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    > <template slot="menuLeft" slot-scope="scope">
<!--      <el-button v-hasPermi="['dbManager:source:refreshedLog']" type="primary" size="small"-->
<!--                 @click="refreshed()">刷新日志</el-button>-->
<!--      <el-button v-hasPermi="['business:asset:export']" type="warning" icon="el-icon-download" size="mini"-->
<!--                 @click="handleExport">导出</el-button>-->
    </template>

    </avue-crud>

    <div>
      <div class="charts-container">
        <div ref="pieChart" class="chart" />
        <div ref="lineChart" class="chart" />
        <div ref="histogramChart" class="chart" />
      </div>
    </div>

  </div>
</template>
<script>
import { addSource, statisticList,statisticChart } from '@/api/audit'
import * as echarts from "echarts";
import { getToken } from '@/utils/auth'
import { testDataConnect } from '@/api/source'
export default {
  name: 'Index',
  components: {},
  data() {
    return {
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue:''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: false,
        addBtn: false,
        editBtn: true,
        delBtn: false,
        selection: false,
        column: [
          // {
          //   label: 'ID',
          //   prop: 'id',
          //   width: 120,
          //   search: true,
          //   searchSpan: 4,
          //   editDisplay: false,
          //   addDisplay: false,
          //   searchBtnText: 'number'
          // },
          {
            label: '日期',
            prop: 'statisticDate',
            search:false,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '数据库名',
            prop: 'databaseName',
            search:true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '表名',
            prop: 'tableName',
            search:true,
            editDisplay: true,
            addDisplay: true,
          },
          {
            label: '操作类型',
            prop: 'actionType',
            type: 'select',
            search: true,
            searchSpan: 4,
            editDisplay: true,
            addDisplay: true,
            dicData: [
              {
                label: '查询',
                value: 'select'
              },
              {
                label: '增加',
                value: 'insert'
              },
              {
                label: '删除',
                value: 'delete'
              },
              {
                label: '修改',
                value: 'update'
              },
              {
                label: '其他',
                value: 'unknown'
              }
            ],
          },
          {
            label: '执行次数',
            prop: 'frequency',
            disabled: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay:false,
            editDisplay:false
          }
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page, this.params)
  },
  methods: {


    initPieChart(pieData) {
      const myChart = echarts.init(this.$refs.pieChart);
      // 将操作类型从英文替换为中文
      const translatedData = {
        删除: pieData.delete,
        插入: pieData.insert,
        查询: pieData.select,
        更新: pieData.update,
        未知: pieData.unknown
      };

      const chartData = Object.keys(translatedData).map(key => ({
        value: translatedData[key],
        name: key
      }));

      const option = {
        title: {
          text: '操作类型',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          left: 'left',
          top: '10px'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#af0d0d',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      };
      myChart.setOption(option);
    },

    initLineChart(lineData) {
      const myChart = echarts.init(this.$refs.lineChart);
      const dates = Object.keys(lineData);
      const values = Object.values(lineData);

      const option = {
        title: {
          text: '每日操作统计',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          left: 'left',
          top: '10px'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '操作数',
            type: 'line',
            data: values,
            itemStyle: {
              color: '#5470C6'
            }
          }
        ]
      };
      myChart.setOption(option);
    },

    initHistogram(histogramData) {
      const myChart = echarts.init(this.$refs.histogramChart);
      const tables = Object.keys(histogramData);
      const values = Object.values(histogramData);

      const option = {
        title: {
          text: '表操作统计',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          },
          left: 'left',
          top: '10px'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: tables
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '操作数',
            type: 'bar',
            data: values,
            itemStyle: {
              color: '#91CC75'
            }
          }
        ]
      };
      myChart.setOption(option);
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      statisticList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })

      statisticChart(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.initPieChart(data.pieData)
        this.initLineChart(data.lineData)
        this.initHistogram(data.histogramData)
      })

    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
<style scoped>
.charts-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.chart {
  flex: 1 1 45%;
  margin: 15px 2.5%;
  border: 1px solid #ddd; /* 定义浅边框 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 定义浅阴影效果 */
  height: 400px;
}
</style>
