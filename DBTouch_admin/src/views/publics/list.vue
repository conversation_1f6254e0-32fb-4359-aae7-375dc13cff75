<!--<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['business:publicRow:export']" type="warning" icon="el-icon-download" size="mini"
                   @click="handleExport">导出</el-button>
      </template>
    </avue-crud>
  </div>
</template>-->

// <script>
// import { getCmsPublicRowS } from '@/api/pubRow'

// export default {
//   name: 'Publics',
//   components: {},
//   data() {
//     return {
//       dialogOrderForm: false,
//       dialogFormVisible: false,
//       orderForm: {},
//       productOption: [],
//       form: {},
//       loading: true,
//       page: {
//         pageNo: 1,
//         pageSize: 10,
//         total: 10
//       },
//       data: []
//     }
//   },
//   computed: {
//     option() {
//       return {
//         selection: false,
//         excelBtn: false,
//         loading: true,
//         border: true,
//         stripe: true,
//         showHeader: true,
//         searchMenuSpan: 4,
//         page: true,
//         align: 'center',
//         menu: false,
//         addBtn: false,
//         editBtn: false,
//         delBtn: false,
//         searchLabelWidth:100,
//         searchLabelPosition:'left',
//         column: [
//           {
//             label: 'ID',
//             prop: 'id',
//             disabled: true,
//             addDisplay: false,
//             editDisplay: false,
//             searchSpan: 4
//           },
//           {
//             label: '账本用户ID',
//             prop: 'userId',
//             disabled: false,
//             addDisplay: true,
//             editDisplay: false,
//             search: true,
//             searchSpan: 4,
//           },
//           {
//             label: '订单ID',
//             prop: 'orderId',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '节点用户ID',
//             prop: 'node',
//             disabled: false,
//             addDisplay: false,
//             search: true,
//             editDisplay: false
//           },
//           {
//             label: '公排位置',
//             prop: 'nodePosition',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '上级节点',
//             prop: 'parentNode',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '公排序号',
//             prop: 'pubNum',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '下级节点',
//             prop: 'childNode',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '下级记账数量',
//             prop: 'childNodeNum',
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '是否有公排节点',
//             prop: 'isPubNode',
//             type: 'select',
//             search: true,
//             searchLabelWidth:120,
//             searchSpan: 4,
//             dicData: [
//               {
//                 label: '否',
//                 value: 0
//               },
//               {
//                 label: '是',
//                 value: 1
//               }
//             ],
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '加入公排方式',
//             prop: 'type',
//             dicData: [
//               {
//                 label: '购买加入',
//                 value: 1
//               },
//               {
//                 label: '直推加入',
//                 value: 2
//               },
//               {
//                 label: '-',
//                 value: 0
//               },
//             ],
//             disabled: false,
//             addDisplay: false,
//             editDisplay: false
//           },
//           {
//             label: '下级节点公排数',
//             prop: 'childPubNum',
//             editDisplay: false,
//             addDisplay: false,
//             disabled: true
//           },
//           {
//             label: '成为公排的时间',
//             prop: 'pubCreateTime',
//             editDisplay: false,
//             addDisplay: false,
//             disabled: true
//           },
//           {
//             label: '创建时间',
//             prop: 'createTime',
//             editDisplay: false,
//             addDisplay: false,
//             disabled: true
//           }
//         ]
//       }
//     }
//   },
//   created() {
//     this.onLoad(this.page, this.params)
//   },
//   methods: {
//     searchReset() {
//       this.query = {}
//       this.onLoad(this.page)
//     },
//     searchChange(params, done) {
//       this.query = params
//       this.page.pageNo = 1
//       this.onLoad(this.page, params)
//       done()
//     },
//     currentChange(pageNo) {
//       this.page.pageNo = pageNo
//       this.onLoad(this.page)
//     },
//     sizeChange(pageSize) {
//       this.page.pageNo = 1
//       this.page.pageSize = pageSize
//       this.onLoad(this.page)
//     },
//     refreshChange() {
//       this.onLoad(this.page, this.query)
//     },
//     onLoad(page, params = {}) {
//       this.loading = true
//       getCmsPublicRowS(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
//         const data = res.data
//         this.data = data.records
//         this.page.pageNo = data.current
//         this.page.pageSize = data.size
//         this.page.total = data.total
//         this.loading = false
//       })
//     },
//     handleExport() {
//       this.download('cmsPublicRow/export', {
//         ...this.query
//       }, `publics_${new Date().getTime()}.xlsx`)
//     }
//   }
// }
// </script>
