<template>
  <div class="app-container">
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @search-change="searchChange"
      @search-reset="searchReset"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @current-change="currentChange"
    >
      <template slot="menuLeft" slot-scope="scope">
        <el-button v-hasPermi="['sharding:config:addBtn']" type="primary" icon="el-icon-plus" size="small"
                   @click="showModal(1)"
        >新 增
        </el-button>
      </template>
      <template #inUse="scope">
        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="switChange(scope.row)"
        ></el-switch>
      </template>
      <template #menu="{row,index}">
        <el-button type="text"
                   size="mini"
                   icon="el-icon-edit"
                   @click="showModal(2,row)"
        >编辑
        </el-button>
        <el-button type="text"
                   size="mini"
                   icon="el-icon-view"
                   @click="showModal(3,row)"
        >查看
        </el-button>
        <el-button type="text"
                   size="mini"
                   icon="el-icon-delete"
                   @click="rowDel(row,index)"
        >删除
        </el-button>
      </template>
    </avue-crud>

    <el-dialog
      :visible.sync="centerDialogVisible"
      :title="modelTitle"
      width="80%"
      align-center
    >
      <el-row>
        <!-- 左侧部分 -->
        <el-col :span="12">
          <div class="grid-content">
            <div class="form-item">
              <span class="form-label">配置名称：</span>
              <el-input v-model="reSplitterConfig.name" :disabled="readOnlyStatus" placeholder="请输入配置名称"
              ></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">数据源：</span>
              <el-select v-model="reSplitterConfig.selectedOption" :disabled="readOnlyStatus" multiple
                         placeholder="请选择数据源" @change="changeConfigInfo(1)" style="width: 100%;"
              >
                <el-option v-for="(item, index) in databaseData" :key="item.id" :label="item.name"
                           :value="item.id"
                >
                </el-option>
              </el-select>
            </div>

            <div class="form-item">
              <span class="form-label">主数据库：</span>
              <el-select v-model="reSplitterConfig.masterConfig" :disabled="readOnlyStatus" multiple
                         placeholder="请选择主数据库" @change="changeConfigInfo" style="width: 100%;"
              >
                <el-option v-for="(item, index) in selectDatabase" :key="item.id" :label="item.name"
                           :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
            <div class="form-item">
              <span class="form-label">从数据库：</span>
              <el-select v-model="reSplitterConfig.slaveConfig" :disabled="readOnlyStatus" multiple
                         placeholder="请选择从数据库" @change="changeConfigInfo" style="width: 100%;"
              >
                <el-option v-for="(item, index) in selectDatabase" :key="item.id" :label="item.name"
                           :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
            <!--            <div class="form-item">-->
            <!--              <span class="form-label">轮询规则：</span>-->
            <!--              <el-select v-model="reSplitterConfig.loadBalancers" :disabled="readOnlyStatus"-->
            <!--                         placeholder="请选择轮询规则" @change="changeConfigInfo" style="width: 100%;"-->
            <!--              >-->
            <!--                <el-option-->
            <!--                  v-for="item in loadBalancersData"-->
            <!--                  :key="item.value"-->
            <!--                  :label="item.label"-->
            <!--                  :value="item.value"-->
            <!--                >-->
            <!--                </el-option>-->
            <!--              </el-select>-->
            <!--            </div>-->
            <!-- 包含所有 el-input 的 box -->
            <div class="input-box border" v-show="weightProps">
              <span class="form-label">权重：</span>
              <!-- 使用 v-for 指令遍历 items 数组生成 el-input 元素 -->
              <el-input
                v-for="(item, index) in selectDatabase"
                :key="index"
                v-model="inputValues[item.id]"
                type="number"
                :disabled="readOnlyStatus"
                @change="changeConfigInfo"
                :placeholder="'请输入' + item.name+'权重'"
                class="input-item"
              ></el-input>
            </div>
            <div class="form-item">
              <span class="form-label">备注：</span>
              <el-input type="textarea" v-model="reSplitterConfig.remake" :disabled="readOnlyStatus" :rows="3"
                        placeholder="请输入备注"
              ></el-input>
            </div>
          </div>
        </el-col>

        <!-- 右侧部分 -->
        <el-col :span="12">
          <div class="grid-content">
            <span class="form-label">配置文件内容：</span>
            <el-input type="textarea" v-model="reSplitterConfig.shardingTextConfig" :disabled="true" :rows="23"
                      placeholder="配置文件内容"
            ></el-input>
          </div>
        </el-col>
      </el-row>
      <template #footer>

        <div class="dialog-footer">
          <div v-if="readOnlyStatus">
            <el-button @click="centerDialogVisible = false">关 闭</el-button>
          </div>
          <div v-else>
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveConfig">
              确 认
            </el-button>
          </div>

        </div>
      </template>
    </el-dialog>

  </div>
</template>
<script>
import { addSource, list, deleteById, makeInUse } from '@/api/msConfig'
import { list as sourceList } from '@/api/source'

export default {
  name: 'Index',
  components: {},
  data() {
    return {
      inputValues: {},
      weightProps: false,
      readOnlyStatus: false,
      addReSplitter: false,
      modelTitle: '新增配置',
      reSplitterConfig: {
        id: '',
        name: '',
        masterConfig: [],
        slaveConfig: [],
        selectedOption: '',
        shardingTextConfig: '',
        remake: '',
        loadBalancers: 0,
        props: {}
      },
      databaseData: '',
      selectDatabase: '',
      loadBalancersData: [
        {
          value: 0,
          label: '轮询'
        },
        {
          value: 1,
          label: '随机'
        },
        {
          value: 2,
          label: '加权随机'
        }
      ],
      selectedSource: '',
      selectedTable: '',
      centerDialogVisible: false,
      dialogOrderForm: false,
      dialogFormVisible: false,
      dialogVisible: false,
      orderForm: {},
      productOption: [],
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      parentValue: ''
    }
  },
  computed: {
    option() {
      return {
        excelBtn: false,
        loading: true,
        border: true,
        stripe: true,
        showHeader: true,
        searchMenuSpan: 4,
        page: true,
        align: 'center',
        menu: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: 'ID',
            prop: 'id',
            search: true,
            searchSpan: 4,
            editDisplay: false,
            addDisplay: false,
            searchBtnText: 'number'
          },
          {
            label: '名称',
            prop: 'configName',
            searchSpan: 4,
            search: true,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '是否启用',
            prop: 'inUse',
            slot: 'avue-switch'
          },
          {
            label: '主服务器',
            prop: 'masterServerStr',
            searchSpan: 4,
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '从服务器',
            prop: 'slaveServerStr',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '备注',
            prop: 'remake',
            search: false,
            editDisplay: true,
            addDisplay: true
          },
          {
            label: '创建时间',
            prop: 'createTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '创建时间',
            prop: 'updateTime',
            disabled: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '开始时间',
            type: 'datetime',
            prop: 'startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: false,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          },
          {
            label: '结束时间',
            type: 'datetime',
            prop: 'endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: false,
            searchSpan: 4,
            viewDisplay: false,
            hide: true,
            addDisplay: false,
            editDisplay: false
          }
        ]
      }
    }
  },
  created() {
    this.getDatabaseList()
    this.onLoad(this.page, this.params)
  },
  methods: {
    rowDel(form, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteById({ id: form.id }).then(resp => {
          if (resp.code === 200) {
            this.$message.success('操作成功！')
            this.centerDialogVisible = false
            this.onLoad(this.page, this.params)
          } else {
            this.$message.error('操作失败！')
          }
        })
      }).catch(() => {
      })
    },
    saveConfig() {
      if (this.readOnlyStatus) {
        this.centerDialogVisible = false
        return
      }
      if (!this.reSplitterConfig.name) {
        this.$message.error('请输入配置名称！')
        return
      }
      var from = {
        id: this.reSplitterConfig.id,
        configName: this.reSplitterConfig.name,
        remake: this.reSplitterConfig.remake,
        loadBalancers: this.reSplitterConfig.loadBalancers,
        props: JSON.stringify(this.reSplitterConfig.props),
        masterServer: this.reSplitterConfig.masterConfig.join(', '),
        slaveServer: this.reSplitterConfig.slaveConfig.join(', '),
        configInfo: this.reSplitterConfig.shardingTextConfig,
        databaseIds: this.reSplitterConfig.selectedOption.join(', ')
      }
      if (!from.databaseIds) {
        this.$message.error('请选择数据源！')
        return
      }
      if (!from.masterServer) {
        this.$message.error('请选择主数据库！')
        return
      }
      if (!from.slaveServer) {
        this.$message.error('请选择从数据库！')
        return
      }
      addSource(from).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功！')
          this.centerDialogVisible = false
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('保存失败！')
        }
      })
    },
    switChange(row) {
      makeInUse({ id: row.id }).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    changeConfigInfo(type) {
      let textConfig = 'dataSources:\n'
      let dataSources = {}
      this.weightProps = false
      if (type === 1) {
        this.selectDatabase = []
      }
      // let loadBalancersName = '      loadBalancerName: '
      // let loadBalancersText =
      //   '  loadBalancers:\n' +
      //   '    round_robin:\n' +
      //   '      type: ROUND_ROBIN\n' +
      //   '    random:\n' +
      //   '      type: RANDOM\n' +
      //   '    weight:\n' +
      //   '      type: WEIGHT\n'
      // if (this.reSplitterConfig.loadBalancers === 0) {
      //   loadBalancersName += 'round_robin\n'
      // } else if (this.reSplitterConfig.loadBalancers === 1) {
      //   loadBalancersName += 'random\n'
      // } else if (this.reSplitterConfig.loadBalancers === 2) {
      //   this.weightProps = true
      //   this.reSplitterConfig.props = {}
      //   console.log(this.inputValues)
      //   loadBalancersName += 'weight\n'
      //   loadBalancersText += '        props:\n'
      //   this.selectDatabase.forEach(dataSource => {
      //     let weigth = this.inputValues[dataSource.id] ? this.inputValues[dataSource.id] : '0'
      //     this.reSplitterConfig.props[dataSource.id] = weigth
      //     loadBalancersText += '          ds' + dataSource.id + ': ' + weigth + '\n'
      //   })
      // }

      this.databaseData.forEach(dataSource => {
        if (this.reSplitterConfig.selectedOption.includes(dataSource.id)) {
          if (type === 1) {
            this.selectDatabase.push(dataSource)
          }
          const dsName = 'ds_' + dataSource.id
          let dataType=''
          textConfig += `  ${dsName}: !!com.zaxxer.hikari.HikariDataSource\n`
          if (dataSource.type === 1) {
            dataType = 'dm'
            textConfig += `    driverClassName: dm.jdbc.driver.DmDriver\n`
          } else if (dataSource.type === 2) {
            dataType = 'kingbase8'
            textConfig += `    driverClassName: com.kingbase8.Driver\n`
          }
          textConfig += `    jdbcUrl: jdbc:`+dataType+`://${dataSource.url}:${dataSource.port}/${dataSource.databaseName}?schema=${dataSource.databaseName}\n`
          textConfig += `    username: ${dataSource.username}\n`
          textConfig += `    password: ${dataSource.password}\n`
        }
      })

      console.log(this.reSplitterConfig)

      if (this.reSplitterConfig.slaveConfig.length > 0 && this.reSplitterConfig.masterConfig.length > 0) {
        console.log("---------------------")
        textConfig += 'masterSlaveRule:\n'
        textConfig += '  name: ds_ms\n'
        if (this.reSplitterConfig.masterConfig.length === 1){
          textConfig += '  masterDataSourceName: ds_'+this.reSplitterConfig.masterConfig[0]+'\n'
        }else {
          let configText = this.reSplitterConfig.masterConfig.map(element => `ds_${element}`);
          textConfig += '  masterDataSourceName: '+'['+configText+']\n'
        }
        // if (this.reSplitterConfig.slaveConfig.length === 1){
        //   textConfig += '  slaveDataSourceNames:ds_'+this.reSplitterConfig.slaveConfig[0]+'\n'
        // }else {
        //   let configText = this.reSplitterConfig.slaveConfig.map(element => `ds_${element}`);
        //   textConfig += '  slaveDataSourceNames:'+'['+configText+']\n'
        // }
        let configText = this.reSplitterConfig.slaveConfig.map(element => `ds_${element}`);
        textConfig += '  slaveDataSourceNames: '+'['+configText+']\n'
      }

      textConfig += 'props:\n' +
        '  sql.show: true';
      this.reSplitterConfig.shardingTextConfig = textConfig
    },

    showModal(type, row) {
      this.readOnlyStatus = false
      if (type === 1) {
        this.modelTitle = '新增配置'
        this.reSplitterConfig.id = null
        this.reSplitterConfig.name = null
        this.reSplitterConfig.remake = null
        this.reSplitterConfig.masterConfig = []
        this.reSplitterConfig.slaveConfig = []
        this.reSplitterConfig.shardingTextConfig = null
        this.reSplitterConfig.loadBalancers = 0
        this.reSplitterConfig.props = null
        this.reSplitterConfig.selectedOption = []
        this.selectDatabase = ''
        this.addSharding = true
      } else if (type === 2) {
        this.modelTitle = '编辑配置'
        this.addSharding = false
        this.reSplitterConfig.id = row.id
        this.reSplitterConfig.name = row.configName
        this.reSplitterConfig.remake = row.remake
        this.reSplitterConfig.masterConfig = row.masterServer ? row.masterServer.split(',').map(Number) : []
        this.reSplitterConfig.slaveConfig = row.slaveServer ? row.slaveServer.split(',').map(Number) : []
        this.reSplitterConfig.shardingTextConfig = row.configInfo
        this.reSplitterConfig.selectedOption = row.databaseIds ? row.databaseIds.split(',').map(Number) : []
        this.reSplitterConfig.loadBalancers = row.loadBalancers
        this.selectDatabase = []
        this.databaseData.forEach(dataSource => {
            if (this.reSplitterConfig.selectedOption.includes(dataSource.id)) {
              this.selectDatabase.push(dataSource)
            }
          }
        )
        this.weightProps = false
        if (row.loadBalancers === 2) {
          this.weightProps = true
          this.reSplitterConfig.props = JSON.parse(row.props)
          this.inputValues = JSON.parse(row.props)
        }

      } else if (type === 3) {
        this.modelTitle = '查看配置'
        this.readOnlyStatus = true
        this.reSplitterConfig.id = row.id
        this.reSplitterConfig.name = row.configName
        this.reSplitterConfig.remake = row.remake
        this.reSplitterConfig.masterConfig = row.masterServer ? row.masterServer.split(',').map(Number) : []
        this.reSplitterConfig.slaveConfig = row.slaveServer ? row.slaveServer.split(',').map(Number) : []
        this.reSplitterConfig.shardingTextConfig = row.configInfo
        this.reSplitterConfig.selectedOption = row.databaseIds ? row.databaseIds.split(',').map(Number) : []
        this.reSplitterConfig.loadBalancers = row.loadBalancers
        this.selectDatabase = []
        this.databaseData.forEach(dataSource => {
            if (this.reSplitterConfig.selectedOption.includes(dataSource.id)) {
              this.selectDatabase.push(dataSource)
            }
          }
        )
        if (row.loadBalancers === 2) {
          this.weightProps = true
          this.reSplitterConfig.props = row.props
          this.inputValues = JSON.parse(row.props)
        }

      }
      this.centerDialogVisible = true
    },
    rowSave(form, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowUpdate(form, index, done, loading) {
      loading()
      addSource(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page, this.params)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    // openRule(id){
    //   this.$router.push('rules?stockId='+id)
    // },

    openOrder(id) {
      this.$router.push('order?capitalId=' + id)
    },

    getDatabaseList() {
      sourceList({ pageNo: 1, pageSize: 100000 }).then(resp => {
        if (resp.code === 200) {
          this.databaseData = resp.data.records
        } else {
          this.$message.error('数据源数据获取失败')
        }
      })
    },

    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },

    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },

    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      list(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    handleExport() {
      this.download('order/export', {
        ...this.query
      }, `userStockInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
<style scoped>
.grid-content {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}

.button-row {
  margin-top: 20px;
}

.ep-bg-purple {
  background: #d3dce6;
}

.ep-bg-purple-light {
  background: #e9eef3;
}

.add {
  width: 100%;
  height: 30px;
  color: #3396fa;
  text-align: center;
  border-top: 1px solid #ccc;
  font-size: 14px;
  line-height: 30px;
  position: sticky;
  bottom: 0px;
  background-color: #fff;
  opacity: 1;
}

.delete-option {
  float: right;
  color: #3396fa;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 5%;
}

.border {
  border: 1px solid #c0ccda; /* 使用 Element UI 提供的默认边框颜色 */
  padding: 10px; /* 内边距 */
}

/* 单个输入框样式 */
.input-item {
  margin-bottom: 10px; /* 底部外边距 */
}
</style>
