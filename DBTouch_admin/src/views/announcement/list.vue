<template>
  <div class="app-container">
    <avue-crud ref="crud"
               :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               @search-change="searchChange"
               @search-reset="searchReset"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @current-change="currentChange"
               @row-save="rowSave"
               @row-update="rowEdit"
               :permission="permission"
    >
    </avue-crud>
  </div>
</template>
<script>
import { getNoticeList, updateNotice,addNotice } from '@/api/announcement'
import { getToken } from '@/utils/auth'

export default {
  data() {
    return {
      form: {},
      loading: true,
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 10
      },
      data: [],
      permission: {}
    }
  },
  computed: {
    option() {
      return {
        border: true,
        stripe: true,
        showHeader: true,
        size: 'small',
        page: true,
        align: 'center',
        menuAlign: 'center',
        searchMenuSpan: 4,
        addBtn: true,
        delBtn: false,
        menu: true,
        editBtn: true,
        labelWidth: 150,
        column: [
          {
            label: 'ID',
            prop: 'id',
            editDisplay: false,
            addDisplay: false
          },
          {
            label: '语种',
            prop: 'languages',
            type: 'select',
            multiple: true,
            hide: true,
            editDisplay: false,
            addDisplay: true,
            slot: true,
            rules: [
              {
                required: true,
                message: '请选择语种',
                trigger: 'blur'
              }
            ],
            dicData: [
              {
                label: '中文',
                value: 'zh'
              },
              {
                label: '英语',
                value: 'en'
              },
              {
                label: '韩语',
                value: 'ko'
              },
              {
                label: '越南语',
                value: 'vi'
              },
              {
                label: '西班牙语',
                value: 'es'
              },
              {
                label: '日语',
                value: 'ja'
              }
            ],
            span: 24
          },
          {
            label: '语种',
            prop: 'language',
            type: 'select',
            search:true,
            editDisplay: true,
            addDisplay: false,
            slot: true,
            rules: [
              {
                required: true,
                message: '请选择语种',
                trigger: 'blur'
              }
            ],
            dicData: [
              {
                label: '中文',
                value: 'zh'
              },
              {
                label: '英语',
                value: 'en'
              },
              {
                label: '韩语',
                value: 'ko'
              },
              {
                label: '越南语',
                value: 'vi'
              },
              {
                label: '西班牙语',
                value: 'es'
              },
              {
                label: '日语',
                value: 'ja'
              }
            ],
            span: 24
          },
          {
            label: '标题',
            prop: 'title',
            type: 'textarea',
            minRows: 3,
            overHidden: true,
            slot: true,
            rules: [
              {
                required: true,
                message: '请输入标题',
                trigger: 'blur'
              }
            ],
            span: 24
          },
          {
            label: '弹出内容',
            prop: 'popupContent',
            type: 'textarea',
            overHidden: true,
            slot: true,
            rules: [
              {
                required: true,
                message: '请输入弹出内容',
                trigger: 'blur'
              }
            ],propsHttp: {
              home: '',
              url: 'url',
              res: 'data'
            },
            headers: { 'Authorization': 'Bearer ' + getToken() },
            //普通图片上传
            action: process.env.VUE_APP_BASE_API + '/notice/upload'

          },
          {
            label: '内容',
            prop: 'content',
            span: 24,
            component: 'avueUeditor',
            slot: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: '请输入内容',
                trigger: 'blur'
              }
            ],
            propsHttp: {
              home: '',
              url: 'url',
              res: 'data'
            },
            headers: { 'Authorization': 'Bearer ' + getToken() },
            //普通图片上传
            action: process.env.VUE_APP_BASE_API + '/notice/upload'
          },
          {
            label: '状态开关',
            prop: 'status',
            type: 'select',
            search:true,
            slot: true,
            rules: [
              {
                required: true,
                message: '请选择状态',
                trigger: 'blur'
              }
            ],
            dicData: [
              {
                label: '关',
                value: 0
              },
              {
                label: '开',
                value: 1
              }
            ]
          },
          {
            label: '过期时间',
            type: 'datetime',
            prop: 'expireTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            editDisplay: true,
            addDisplay: true,
          },
        ]
      }
    }
  },
  created() {
    this.onLoad(this.page)
    this.setPermission()
  },
  methods: {
    setPermission() {
      const value = ['business:notice:edit']
      const all_permission = '*:*:*'
      const permissions = this.$store.getters && this.$store.getters.permissions
      if (value && value instanceof Array && value.length > 0) {
        const permissionFlag = value
        const hasPermissions = permissions.some(permission => {
          return all_permission === permission || permissionFlag.includes(permission)
        })
        this.permission = {
          editBtn: hasPermissions
        }
      }
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.pageNo = 1
      this.onLoad(this.page, params)
      done()
    },
    currentChange(pageNo) {
      this.page.pageNo = pageNo
      this.onLoad(this.page)
    },
    sizeChange(pageSize) {
      this.page.pageNo = 1
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getNoticeList(page.pageNo, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data
        this.data = data.records
        this.page.pageNo = data.current
        this.page.pageSize = data.size
        this.page.total = data.total
        this.loading = false
      })
    },
    rowSave(form, done, loading) {
      loading()
      addNotice(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page)
        } else {
          this.$message.error(resp.msg)
        }
      })
    },
    rowEdit(form, index, done, loading) {
      loading = true
      updateNotice(form).then(resp => {
        if (resp.code === 200) {
          this.$message.success('操作成功')
          done(form)
          this.onLoad(this.page)
        } else {
          this.$message.error(resp.msg)
        }
        loading = false
      })
    }
  }
}
</script>
