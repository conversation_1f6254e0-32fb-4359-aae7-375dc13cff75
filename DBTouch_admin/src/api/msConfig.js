import request from '@/utils/request'


// 套餐兑换码列表
export function list(pageNo, pageSize, params) {
  return request({
    url: '/masterSlave/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addSource(data) {
  return request({
    url: '/masterSlave/save',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/masterSlave/delete',
    method: 'post',
    data
  })
}

export function makeInUse(data) {
  return request({
    url: '/masterSlave/makeInUse',
    method: 'post',
    data
  })
}



