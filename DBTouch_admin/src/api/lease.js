import request from '@/utils/request'

// 租赁配置查询
export function getConfigList() {
  return request({
    url: '/lease/config',
    method: 'get'
  })
}

//租赁配置修改
export function updateConfig(data) {
  return request({
    url: '/lease/update',
    method: 'post',
    data
  })
}

// 出租记录查询
export function getLeaseList(pageNo, pageSize, params) {
  return request({
    url: '/lease/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
// 出租收益记录查询
export function getLeaseIncomeList(params) {
  return request({
    url: '/lease/incomeList',
    method: 'get',
    params
  })
}
// 出租收益记录查询
export function output() {
  return request({
    url: '/user/output',
    method: 'get'
  })
}
// 出租收益记录查询
export function leaseReward() {
  return request({
    url: '/user/leaseReward',
    method: 'get'
  })
}

// 出租收益记录查询
export function updateLease(data) {
  return request({
    url: '/lease/updateLease',
    method: 'post',
    data
  })
}
