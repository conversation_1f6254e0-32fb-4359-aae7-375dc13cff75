import request from '@/utils/request'

export function list(pageNo, pageSize, params) {
  return request({
    url: '/source/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addSource(data) {
  return request({
    url: '/source/save',
    method: 'post',
    data
  })
}

export function makeInUse(data) {
  return request({
    url: '/source/makeInUse',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/source/delete',
    method: 'post',
    data
  })
}

export function testDataConnect(data) {
  return request({
    url: '/dbManager/testConnect',
    method: 'post',
    data
  })
}

export function getTableColumns(data) {
  return request({
    url: '/dbManager/getTableColumns',
    method: 'post',
    data
  })
}

export function getTableNames(data) {
  return request({
    url: '/dbManager/getTableNames',
    method: 'post',
    data
  })
}

export function getDatabaseNames(data) {
  return request({
    url: '/dbManager/getDatabaseNames',
    method: 'post',
    data
  })
}
