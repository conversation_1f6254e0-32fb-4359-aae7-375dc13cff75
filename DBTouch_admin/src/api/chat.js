import request from '@/utils/request'


// 公告列表
export function getMessage(pageNo, pageSize, params) {
  return request({
    url: '/chat/getMessage',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
export function getChatMessage(pageNo, pageSize, params) {
  return request({
    url: '/chat/getChatMessage',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

// 修改公告
export function sendMessage(data) {
  return request({
    url: '/chat/sendMessage',
    method: 'post',
    data
  })
}

// 上传文件
export function chatUpload (data){
  return request({
      url: '/chat/upload',
      method: 'post',
      headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": "Bearer "+data.token,
      },
      data:data.formData
  })
}