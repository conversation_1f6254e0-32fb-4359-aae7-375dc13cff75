import request from '@/utils/request'


// 套餐兑换码列表
export function list(pageNo, pageSize, params) {
  return request({
    url: '/rwSplit/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addSource(data) {
  return request({
    url: '/rwSplit/save',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/rwSplit/delete',
    method: 'post',
    data
  })
}

export function makeInUse(data) {
  return request({
    url: '/rwSplit/makeInUse',
    method: 'post',
    data
  })
}



