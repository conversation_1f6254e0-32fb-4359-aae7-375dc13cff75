import request from '@/utils/request'


// 套餐兑换码列表
export function list(pageNo, pageSize, params) {
  return request({
    url: '/order/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function vipList(pageNo, pageSize, params) {
  return request({
    url: '/order/vip/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

//

export function payRecordList(pageNo, pageSize, params) {
  return request({
    url: '/order/payRecord/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

