import request from '@/utils/request'


// 套餐兑换码列表
export function codeList(pageNo, pageSize, params) {
  return request({
    url: '/card/code/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

// 套餐兑换码列表
export function recordList(pageNo, pageSize, params) {
  return request({
    url: '/card/record/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

//

export function templateList(pageNo, pageSize, params) {
  return request({
    url: '/card/template/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addTemplate(data) {
  return request({
    url: '/card/template/add',
    method: 'post',
    data
  })
}

export function deleteTemplate(data) {
  return request({
    url: '/card/template/remove',
    method: 'post',
    data
  })
}
