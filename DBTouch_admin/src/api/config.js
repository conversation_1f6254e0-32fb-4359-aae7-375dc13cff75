import request from '@/utils/request'


export function getVipLevel(pageNo, pageSize, params) {
  return request({
    url: '/config/vipLevel/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getVersions(pageNo, pageSize, params) {
  return request({
    url: '/config/versions/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
export function getTaskList(pageNo, pageSize, params) {
  return request({
    url: '/config/taskList/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addVip(data) {
  return request({
    url: '/config/vipLevel/add',
    method: 'post',
    data
  })
}

export function addVersion(data) {
  return request({
    url: '/config/versions/add',
    method: 'post',
    data
  })
}

export function addTask(data) {
  return request({
    url: '/config/taskList/add',
    method: 'post',
    data
  })
}
