import request from '@/utils/request'


export function list(pageNo, pageSize, params) {
  return request({
    url: '/auditLog/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addSource(data) {
  return request({
    url: '/auditLog/save',
    method: 'post',
    data
  })
}

export function refreshedLog(data) {
  return request({
    url: '/dbManager/refreshedLog',
    method: 'post',
    data
  })
}


export function statisticList(pageNo, pageSize, params) {
  return request({
    url: '/dailyStatistic/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function statisticChart(pageNo, pageSize, params) {
  return request({
    url: '/dailyStatistic/chart',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

