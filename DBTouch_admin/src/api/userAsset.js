import request from '@/utils/request'

export function getAssetRecordList(pageNo, pageSize, params) {
  return request({
    url: '/user/asset/record/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}


// 新增资产记录
export function recordEdit(data) {
  return request({
    url: '/user/asset/record/edit',
    method: 'post',
    data
  })
}

export function getBuyRecords(userId,address){
  return request({
    url:'/user/asset/getBuyRecords',
    method: 'get',
    params: {
      userId,
      address
    }
  })
}
export function getUserOrderForAdd(userId){
  return request({
    url:'/investment/incomeList/validOrder',
    method: 'get',
    params: {
      userId
    }
  })
}
