import request from '@/utils/request'


// 公告列表
export function getNoticeList(pageNo, pageSize, params) {
  return request({
    url: '/notice/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
// 修改公告
export function updateNotice(data) {
  return request({
    url: '/notice/update',
    method: 'post',
    data
  })
}

export function addNotice(data) {
  return request({
    url: '/notice/add',
    method: 'post',
    data
  })
}
