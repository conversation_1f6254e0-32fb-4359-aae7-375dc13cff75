import request from '@/utils/request'


export function list(pageNo, pageSize, params) {
  return request({
    url: '/userDds/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function adduserDds(data) {
  return request({
    url: '/userDds/save',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/userDds/delete',
    method: 'post',
    data
  })
}


