import request from '@/utils/request'

export function getWithdrawRecord(pageNo, pageSize, params) {
  return request({
    url: '/withdrawal/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
export function withdrawalGoback(data) {
  return request({
    url: '/withdrawal/goback',
    method: 'post',
    data
  })
}
export function withdrawalSync(data) {
  return request({
    url: '/withdrawal/sync',
    method: 'post',
    data
  })
}
export function orderCheck(data) {
  return request({
    url: '/withdrawal/order/check',
    method: 'post',
    data
  })
}
export function startBatch(data) {
  return request({
    url: '/withdrawal/startbatch',
    method: 'post',
    data
  })
}
export function check(data) {
  return request({
    url: '/withdrawal/check',
    method: 'post',
    data
  })
}

export function getWithdrawWhitelistList(pageNo, pageSize, params) {
  return request({
    url: '/withdrawal/whitelist/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addWhitelist(data) {
  return request({
    url: '/withdrawal/whitelist/add',
    method: 'post',
    data
  })
}

export function batchAddWhitelist(data) {
  return request({
    url: '/withdrawal/whitelist/batch/add',
    method: 'post',
    data
  })
}

export function removeWhitelist(data) {
  return request({
    url: '/withdrawal/whitelist/remove',
    method: 'post',
    data
  })
}

export function withdrawalToken(params) {
  return request({
    url: '/withdrawal/token',
    method: 'get',
    params
  })
}

export function withdrawalBurn(params) {
  return request({
    url: '/withdrawal/burn',
    method: 'get',
    params
  })
}
