import request from '@/utils/request'


export function list(pageNo, pageSize, params) {
  return request({
    url: '/dds/config/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addShardingConfig(data) {
  return request({
    url: '/dds/config/save',
    method: 'post',
    data
  })
}

export function makeInUse(data) {
  return request({
    url: '/dds/config/makeInUse',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/dds/config/delete',
    method: 'post',
    data
  })
}

export function infoList(pageNo, pageSize, params) {
  return request({
    url: '/dds/info/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addDdsInfo(data) {
  return request({
    url: '/dds/info/save',
    method: 'post',
    data
  })
}

export function makeInfoInUse(data) {
  return request({
    url: '/dds/info/makeInUse',
    method: 'post',
    data
  })
}

export function deleteInfoById(data) {
  return request({
    url: '/dds/info/delete',
    method: 'post',
    data
  })
}


export function rtpsConfigList(pageNo, pageSize, params) {
  return request({
    url: '/dds/rtpsConfig/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addRtpsConfig(data) {
  return request({
    url: '/dds/rtpsConfig/save',
    method: 'post',
    data
  })
}

export function makeInUseRtpsConfig(data) {
  return request({
    url: '/dds/rtpsConfig/makeInUse',
    method: 'post',
    data
  })
}

export function deleteRtpsConfigById(data) {
  return request({
    url: '/dds/rtpsConfig/delete',
    method: 'post',
    data
  })
}
