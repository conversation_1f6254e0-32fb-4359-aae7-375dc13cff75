import request from '@/utils/request'

export function getUserStatistics() {
  return request({
    url: '/statistics/user',
    method: 'get'
  })
}

export function getstatisticsAll() {
  return request({
    url: '/statistics/all',
    method: 'get'
  })
}
// 后台统计 - 昨日
export function statisticsYesterday() {
  return request({
    url: '/statistics/yesterday',
    method: 'get'
  })
}

// 后台统计 - 注册&激活
export function statisticsRegisterActive() {
  return request({
    url: '/statistics/register/active',
    method: 'get'
  })
}

// 后台统计 - 当前价格
export function statisticsPrice() {
  return request({
    url: '/statistics/price',
    method: 'get'
  })
}
// 后台统计 - 购买
// export function statisticsBuy() {
//   return request({
//     url: '/statistics/buy',
//     method: 'get'
//   })
// }
// // 后台统计 - 公排
// export function statisticsPublic() {
//   return request({
//     url: '/statistics/public',
//     method: 'get'
//   })
// }

// 后台统计 - 用户资产
export function statisticsAsset() {
  return request({
    url: '/statistics/asset',
    method: 'get'
  })
}

// 后台统计 - 充值&提现
export function statisticsRechargeWithdraw() {
  return request({
    url: '/statistics/recharge/withdraw',
    method: 'get'
  })
}