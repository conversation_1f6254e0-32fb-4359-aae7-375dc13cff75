import request from '@/utils/request'


// 套餐兑换码列表
export function list(pageNo, pageSize, params) {
  return request({
    url: '/sharding/config/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addShardingConfig(data) {
  return request({
    url: '/sharding/config/save',
    method: 'post',
    data
  })
}

export function makeInUse(data) {
  return request({
    url: '/sharding/config/makeInUse',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/sharding/config/delete',
    method: 'post',
    data
  })
}

