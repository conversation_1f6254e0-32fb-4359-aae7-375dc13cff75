import request from '@/utils/request'

// 动态收益config列表
export function getDynamicConfigList() {
  return request({
    url: '/investment/dynamicsconfig',
    method: 'get'
  })
}
//静态收益config列表
export function getStaticConfigList() {
  return request({
    url: '/investment/staticconfig',
    method: 'get'
  })
}
//出局倍数列表
export function getMutipleList() {
  return request({
    url: '/investment/multiple',
    method: 'get'
  })
}
//倍数编辑
export function updateMultiple(data) {
  return request({
    url: '/investment/multipleupdate',
    method: 'post',
    data
  })
}
//静态收益修改
export function updateStatiConfig(data) {
  return request({
    url: '/investment/staticconfigupdate',
    method: 'post',
    data
  })
}
//动态收益修改
export function updateDynamicConfig(data) {
  return request({
    url: '/investment/dynamicsconfigupdate',
    method: 'post',
    data
  })
}
// 用户理财资产     pageNo, pageSize, userId,status
export function getInvestmentList(pageNo, pageSize, params) {
  return request({
    url: '/investment/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
// 产品购买记录
export function getInvestmentOrderList(pageNo, pageSize, params) {
  return request({
    url: '/investment/order/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
//updateInvestmentOrder
export function updateInvestmentOrder(data) {
  return request({
    url: '/investment/updateInvestmentOrder',
    method: 'post',
    data
  })
}
export function getInvestmentIncomeList(pageNo, pageSize, params) {
  return request({
    url: '/investment/incomeList',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
 