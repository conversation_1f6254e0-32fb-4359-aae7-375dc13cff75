import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function getUserList(pageNo, pageSize, params) {
  return request({
    url: '/reg/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getIdauthRequest(pageNo, pageSize, params) {
  return request({
    url: '/reg/idauthRequest/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getPartnerList(pageNo, pageSize, params) {
  return request({
    url: '/user/partner/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function updateUserLevel(data) {
  return request({
    url: '/user/level/update',
    method: 'post',
    data
  })
}

export function updateUserNode(data) {
  return request({
    url: '/user/node/update',
    method: 'post',
    data
  })
}

export function updateInvestmentNode(data) {
  return request({
    url: '/user/investmentnode/update ',
    method: 'post',
    data
  })
}
export function addUser(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}

export function batchAddUser(data) {
  return request({
    url: '/user/batch/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}

export function getUserTeamList(pageNo, pageSize, params) {
  return request({
    url: '/user/getUserTeamList',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
export function userGetPath( params) {
  return request({
    url: '/user/getPath',
    method: 'get',
    params
  })
}
