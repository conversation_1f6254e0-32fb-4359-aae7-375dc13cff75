import request from '@/utils/request'

export function getList(pageNo, pageSize, params) {
  return request({
    url: '/capital/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function addCapital(data) {
  return request({
    url: '/capital/save',
    method: 'post',
    data
  })
}

export function updateCapital(data) {
  return request({
    url: '/capital/update',
    method: 'post',
    data
  })
}
export function getOrderList(pageNo, pageSize, params) {
  return request({
    url: '/capital/orderList',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getUnEndList(pageNo, pageSize, params) {
  return request({
    url: '/capital/unEndList',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}





