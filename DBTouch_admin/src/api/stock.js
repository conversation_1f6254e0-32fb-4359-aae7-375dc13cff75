import request from '@/utils/request'


export function getUserStockList(pageNo, pageSize, params) {
  return request({
    url: '/invest/stock/user/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getInvestStockOrderList(pageNo, pageSize, params) {
  return request({
    url: '/invest/stock/order/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}

export function getInvestStockIncomeList(pageNo, pageSize, params) {
  return request({
    url: '/invest/stock/incomeList',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}


export function getWithdrawalStockWhitelistList(pageNo, pageSize, params) {
  return request({
    url: '/stock/withdrawal/whitelist/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
//CmsWithdrawalStockWhitelistDto
export function addStockWhitelist(data) {
  return request({
    url: '/stock/withdrawal/whitelist/add',
    method: 'post',
    data
  })
}

export function batchAddStockWhitelist(data) {
  return request({
    url: '/stock/withdrawal/whitelist/batch/add',
    method: 'post',
    data
  })
}

export function removeStockWhitelist(data) {
  return request({
    url: '/stock/withdrawal/whitelist/remove',
    method: 'post',
    data
  })
}

export function getStockWithdrawRecord(pageNo, pageSize, params) {
  return request({
    url: '/stock/withdrawal/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
export function withdrawalGoback(data) {
  return request({
    url: '/stock/withdrawal/goback',
    method: 'post',
    data
  })
}

export function orderCheck(data) {
  return request({
    url: '/stock/withdrawal/order/check',
    method: 'post',
    data
  })
}
export function startBatch(data) {
  return request({
    url: '/stock/withdrawal/startbatch',
    method: 'post',
    data
  })
}
export function check(data) {
  return request({
    url: '/stock/withdrawal/check',
    method: 'post',
    data
  })
}

// 动态收益config列表
export function getDynamicConfigList() {
  return request({
    url: '/invest/stock/dynamicsconfig',
    method: 'get'
  })
}

export function updateDynamicConfig(data) {
  return request({
    url: '/invest/stock/dynamicsconfigupdate',
    method: 'post',
    data
  })
}


export function getRecordList(pageNo, pageSize, params) {
  return request({
    url: '/invest/stock/record/list',
    method: 'get',
    params: {
      ...params,
      pageNo,
      pageSize
    }
  })
}
